@extends('layouts.app')

@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ setting('site.title') }}</title>
    <meta name="description" content="{{ setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">
@endsection

@section('styles')
    @parent <!-- Include parent styles -->
    <style>
        .hw-18 {
            height: 18px!important;
            min-width: 18px!important;
            width: 18px!important;
        }
        .container {
            border-radius: 10px;
        }
        .table-responsive-lg {
            margin-top: 20px;
        }
        .table-lg th, .table-lg td {
            padding: 1rem .75rem;
            text-align: center;
            margin-left: -90px;
        }
        .table-borderless tbody+tbody, .table-borderless td, .table-borderless th, .table-borderless thead th {
            border: 0;
        }
        .bg-light {
            background-color: #f8f9fa!important;
        }
        .bg-white {
            background-color: #fff!important;
        }
        .font-weight-bold {
            font-weight: 700;
        }
        .font-weight-normal {
            font-weight: 400;
        }
        .font-size-sm {
            font-size: 0.875rem;
        }
        .say-yes::before {
            content: "";
            vertical-align: middle;
            display: inline-block;
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg height='24' width='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%2314b89c' d='M9.707 19.121a.997.997 0 01-1.414 0l-5.646-5.647a1.5 1.5 0 010-2.121l.707-.707a1.5 1.5 0 012.121 0L9 14.171l9.525-9.525a1.5 1.5 0 012.121 0l.707.707a1.5 1.5 0 010 2.121z'/%3E%3C/svg%3E");
            height: 1.5rem;
            width: 1.5rem;
        }
        .badge-light {
            color: #101729;
            background-color: #f1f4f9;
        }
        .badge {
            display: inline-block;
            padding: .25em .4em;
            font-size: 75%;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: .25rem;
            transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        }
        .smooth-btn {
            border-radius: 20px;
            padding: 10px 20px;
            background-color: #0EDC8D;
            color: #fff !important;
            transition: all 0.3s ease;
        }
        .smooth-btn:hover {
            background-color: #20c997;
        }
        /* Additional styles for smooth animations and shadow box */
        .breadcrumbs-content {
            animation: fadeIn 1.5s ease-in-out;
        }
        .price {
        font-family: Arial, Helvetica, sans-serif;
        font-size: 1.5em;
        font-weight: bold;
        color: #315B58;
    }
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        .table-responsive-lg {
            animation: fadeInTable 1.5s ease-in-out;
        }
        @keyframes fadeInTable {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .table-lg th, .table-lg td {
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }
        .table-lg th:hover, .table-lg td:hover {
            background-color: #f1f4f9;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
@endsection

@section('content')

<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title">Pricing</h1>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
                        been the industry's standard dummy text</p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="{{ route('home') }}">Home</a></li>
                    <li>Pricing</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End Breadcrumbs -->

<div class="container">
    <div class="table-responsive-lg" data-aos="fade-up">
        <table class="table table-lg table-hover table-borderless bg-white">
            <thead class="border-bottom">
                <tr class="text-center">
                    <th scope="col" class="text-left">
                        <div class="mb-0">
                            <div class="h5 font-weight-bold" style="margin-bottom:-100px !important;">Choose a Plan</div>
                            <p class="font-weight-normal text-muted font-size-sm d-lg-none">Scroll to view more plans --&gt;</p>
                        </div>
                    </th>
                    @foreach($plans as $plan)
                        <th scope="col" class="text-center text-nowrap">
                            <div class="h5 font-weight-bold mb-0">{{ $plan->name }}</div>
                        </th>
                    @endforeach
                </tr>
                <tr class="text-center">
                    <th scope="col" class="text-left"></th>
                    @foreach($plans as $plan)
                        <th scope="col" class="text-center text-nowrap">
                            <p class="price" >{{ $plan->price }}</p>
                            <p class="font-weight-bold" style="font-size: 1.1em;">{{ $plan->duration }}</p>
                        </th>
                    @endforeach
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th class="font-weight-normal" scope="row">Quran (For kids & Adults)</th>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center">-</td>
                </tr>
                <tr>
                    <th class="font-weight-normal" scope="row">Arabic (For kids & Adults)</th>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center">-</td>
                </tr>
                <tr>
                    <th class="font-weight-normal" scope="row">7 Qeraat
                        <span class="badge badge-light border rounded-circle hw-18" data-toggle="popover" role="button" data-trigger="hover" data-placement="top" data-content="Group Messaging is characterized by an SMS conversation among 3 or more recipients at the same time." data-original-title="" title="">?</span>
                    </th>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                    <td class="text-center"><span class="say-yes"></span></td>
                    <td class="text-center"><span class="say-yes"></span></td>
                </tr>
                <tr>
                    <th class="font-weight-normal" scope="row">Igazh Program
                        <span class="badge badge-light border rounded-circle hw-18" data-toggle="popover" role="button" data-trigger="hover" data-placement="top" data-content="Group Messaging is characterized by an SMS conversation among 3 or more recipients at the same time." data-original-title="" title="">?</span>
                    </th>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                    <td class="text-center"><span class="say-yes"></span></td>
                </tr>
                <tr class="text-center bg-white">
                    <th scope="col"></th>
                    @foreach($plans as $plan)
                        <th scope="col" class="text-center text-nowrap">
                            <a href="{{ route('trial-class') }}" class="btn btn-sm waves-effect mt-3 smooth-btn" role="button">{{ $plan->name }}</a>
                        </th>
                    @endforeach
                </tr>
            </tbody>
        </table>
    </div>
</div>
@endsection

@section('scripts')
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    <script>
        $(document).ready(function() {
            $('[data-toggle="popover"]').popover();
            AOS.init({
                duration: 1000,
                easing: 'ease-in-out',
            });
        });
    </script>
@endsection
