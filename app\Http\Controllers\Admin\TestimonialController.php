<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use TCG\Voyager\Http\Controllers\VoyagerBaseController;
use App\Models\Testimonial;

class TestimonialController extends VoyagerBaseController
{
    public function show(Request $request, $id)
    {
        // Mark as read when viewing
        $testimonial = Testimonial::findOrFail($id);
        if (!$testimonial->is_read) {
            $testimonial->update(['is_read' => true]);
        }

        return parent::show($request, $id);
    }

    public function edit(Request $request, $id)
    {
        // Mark as read when editing
        $testimonial = Testimonial::findOrFail($id);
        if (!$testimonial->is_read) {
            $testimonial->update(['is_read' => true]);
        }

        return parent::edit($request, $id);
    }
}
