<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use TCG\Voyager\Models\DataType;
use TCG\Voyager\Models\DataRow;
use TCG\Voyager\Models\Permission;

class ContactTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create or update DataType for contacts
        $dataType = DataType::firstOrNew(['name' => 'contact']);
        if (!$dataType->exists) {
            $dataType->fill([
                'slug' => 'contacts',
                'name' => 'contact',
                'display_name_singular' => 'Contact',
                'display_name_plural' => 'Contacts',
                'icon' => 'voyager-mail',
                'model_name' => 'App\\Models\\Contact',
                'policy_name' => null,
                'controller' => 'App\\Http\\Controllers\\Admin\\ContactController',
                'generate_permissions' => 1,
                'description' => 'Manage contact messages with approval workflow',
                'server_side' => 1,
                'details' => json_encode([
                    'order_column' => 'created_at',
                    'order_display_column' => 'name',
                    'order_direction' => 'desc',
                    'default_search_key' => 'name',
                    'scope' => null
                ])
            ])->save();
        }

        // Create DataRows for contacts
        $dataRows = [
            [
                'field' => 'id',
                'type' => 'number',
                'display_name' => 'ID',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 1,
            ],
            [
                'field' => 'name',
                'type' => 'text',
                'display_name' => 'Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 2,
            ],
            [
                'field' => 'email',
                'type' => 'text',
                'display_name' => 'Email',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 3,
            ],
            [
                'field' => 'phone',
                'type' => 'text',
                'display_name' => 'Phone',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 4,
            ],
            [
                'field' => 'subject',
                'type' => 'text',
                'display_name' => 'Subject',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 5,
            ],
            [
                'field' => 'message',
                'type' => 'text_area',
                'display_name' => 'Message',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 6,
            ],
            [
                'field' => 'is_approved',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 7,
                'details' => json_encode([
                    'default' => '0',
                    'options' => [
                        '0' => 'No',
                        '1' => 'Yes'
                    ]
                ])
            ],
            [
                'field' => 'is_read',
                'type' => 'checkbox',
                'display_name' => 'Read',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'order' => 8,
                'details' => json_encode([
                    'on' => 'Read',
                    'off' => 'Unread',
                    'checked' => false
                ])
            ],
            [
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 9,
            ],
            [
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 10,
            ],
        ];

        foreach ($dataRows as $row) {
            $dataRow = DataRow::firstOrNew([
                'data_type_id' => $dataType->id,
                'field' => $row['field']
            ]);

            if (!$dataRow->exists) {
                $dataRow->fill($row)->save();
            }
        }

        // Create permissions
        Permission::generateFor('contacts');
    }
}
