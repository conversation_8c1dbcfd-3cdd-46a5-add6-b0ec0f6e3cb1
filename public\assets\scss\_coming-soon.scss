/*======================================
	Coming Soon CSS
========================================*/

.coming-soon {
    height: 100vh;
    text-align: center;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background: $black;

    .verticle-lines .vlines {
        width: 3px;
        height: 100%;
        background: #473BF036;
        position: absolute;
        top: 0;
        bottom: 0;
        -webkit-transform: scaleY(0);
        -ms-transform: scaleY(0);
        transform: scaleY(0);
        -webkit-transform-origin: top left;
        -ms-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-name: lineanim;
        animation-name: lineanim;
        -webkit-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
        -webkit-animation-timing-function: cubic-bezier(.785, .135, .15, .86);
        animation-timing-function: cubic-bezier(.785, .135, .15, .86);
        -webkit-animation-duration: 3s;
        animation-duration: 3s;
        opacity: 0.2;
    }

    .verticle-lines .vlines.one {
        left: 20%;
        -webkit-animation-delay: 1s;
        animation-delay: 1s
    }

    .verticle-lines .vlines.two {
        left: 40%;
        -webkit-animation-delay: 0s;
        animation-delay: 0s;
    }

    .verticle-lines .vlines.three {
        left: 60%;
        -webkit-animation-delay: 2s;
        animation-delay: 2s
    }

    .verticle-lines .vlines.four {
        left: 80%;
        -webkit-animation-delay: 1.5s;
        animation-delay: 1.5s
    }


    @-webkit-keyframes lineanim {
        50% {
            -webkit-transform: scaleY(1);
            transform: scaleY(1);
            -webkit-transform-origin: top left;
            transform-origin: top left
        }

        50.1% {
            -webkit-transform-origin: bottom left;
            transform-origin: bottom left
        }

        100% {
            -webkit-transform: scaleY(0);
            transform: scaleY(0);
            -webkit-transform-origin: bottom left;
            transform-origin: bottom left
        }
    }

    @keyframes lineanim {
        50% {
            -webkit-transform: scaleY(1);
            transform: scaleY(1);
            -webkit-transform-origin: top left;
            transform-origin: top left
        }

        50.1% {
            -webkit-transform-origin: bottom left;
            transform-origin: bottom left
        }

        100% {
            -webkit-transform: scaleY(0);
            transform: scaleY(0);
            -webkit-transform-origin: bottom left;
            transform-origin: bottom left
        }
    }
}

.d-table {
    width: 100%;
    height: 100%;
}

.d-table {
    display: table !important;
}

.d-table-cell {
    vertical-align: middle;
}

.d-table-cell {
    display: table-cell !important;
}

.coming-soon .soon-content {
    text-align: center;

    .text {
        margin-bottom: 60px;

        h2 {
            font-size: 45px;
            font-weight: 700;
            line-height: 52px;
            color: $white;
            display: block;
            margin-bottom: 20px;
            text-transform: capitalize;
        }

        p {
            color: #d0d0d0;
        }
    }

    .box {
        background: #ffffff0d;
        width: 100px;
        height: 100px;
        margin: 0 5px;
        display: inline-block;
        padding-top: 25px;
    }

    h2 {
        text-align: center;
        padding-top: 2px;
        color: $white;
        font-size: 14px;
        font-weight: 400;
    }

    .box h1 {
        font-size: 24px;
        text-align: center;
        font-weight: 700;
        margin-bottom: 5px;
        color: $white;
    }

    .alert {
        display: none;
    }


    .social-links {
        margin-top: 70px;
        display: block;

        h3 {
            font-size: 15px;
            font-weight: 500;
            color: $white;
        }

        .social {
            margin-top: 30px;

            li {
                display: inline-block;
                margin-right: 5px;


                &:last-child {
                    margin: 0;
                }

                a {
                    height: 40px;
                    width: 40px;
                    line-height: 40px;
                    text-align: center;
                    font-size: 12px;
                    border: 1px solid #eee;
                    border-radius: 50%;
                    color: $white;
                    background: transparent;

                    &:hover {
                        border-color: transparent;
                        color: $white;
                        background-color: $theme-color;
                    }
                }
            }
        }
    }
}