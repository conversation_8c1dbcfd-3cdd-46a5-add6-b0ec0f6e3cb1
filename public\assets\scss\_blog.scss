/*======================================
    Blog CSS
========================================*/
.latest-news-area {
    background-color: $gray;

    &.blog-grid-page {
        .pagination {
            margin: 0;
            margin-top: 40px;
        }

        .single-news {
            margin-bottom: 30px;
        }
    }

    &.style2 {
        background-color: $gray;

        .single-news {
            margin-top: 30px;

            &.big {
                .title {
                    line-height: 35px;

                    a {
                        font-size: 25px;
                    }
                }
            }
        }
    }

    .single-news {
        background-color: $white;
        transition: all 0.4s ease-in-out;
        border: 1px solid #eee;

        .meta-data {
            ul {
                li {
                    display: inline-block;
                    margin-right: 20px;

                    &:last-child {
                        margin: 0;
                    }

                    i {
                        color: $theme-color;
                        font-size: 14px;
                        display: inline-block;
                        margin-right: 4px;
                    }

                    a {
                        color: #888;
                        font-size: 13px;
                        font-weight: 500;

                        &:hover {
                            color: $theme-color;
                        }
                    }
                }
            }
        }

        .image {
            position: relative;
            overflow: hidden;

            a {
                width: 100%;
            }

            img {
                height: 100%;
                width: 100%;
                transition: all 0.4s ease-in-out;
            }
        }

        .content-body {
            background-color: $white;
            padding: 30px;

            .cat {
                color: #888;
                font-size: 13px;

                &:hover {
                    color: $theme-color;
                }
            }

            .title {
                margin-top: 20px;
                line-height: 28px;

                a {
                    color: $black;
                    font-size: 18px;
                    font-weight: 700;

                    &:hover {
                        color: $theme-color;
                    }
                }


            }

            p {
                color: #888;
                margin-top: 15px;
            }

            .button {
                margin-top: 30px;

                .btn {
                    font-size: 13px;
                }
            }
        }

        &:hover {
            box-shadow: 0px 0px 20px #00000012;
        }

        &:hover .image .thumb {
            transform: scale(1.1);
        }
    }
}

.blog-list {
    .single-news {
        margin-bottom: 40px;
    }
}

/* News Details */
.blog-single {
    background: $gray;

    .post-details {
        padding: 40px;
        background-color: $white;
        margin: 0;

        .detail-inner {
            background-color: #fff;
            padding: 0;
        }

        .post-thumbnils {
            margin-bottom: 30px;
        }

        .post-title {
            font-weight: 700;
            margin-top: 8px;
            line-height: 38px;
        }

        .post-title a {
            font-size: 27px;
            font-weight: 700;

            &:hover {
                color: $theme-color;
            }

        }

        .post-meta li {
            font-weight: 500;
            font-size: 15px;
            margin-right: 25px;
        }

        .post-meta li a i {
            font-weight: 400;
            margin-right: 3px;
        }

        .post-meta li a:hover {
            color: $theme-color;
        }

        p {
            font-size: 14px;
            margin: 30px 0;
            line-height: 24px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        h3 {
            font-size: 22px;
            margin-bottom: 20px;
            font-weight: 600;
            line-height: 28px;
        }

        .list {
            li {
                display: block;
                margin-bottom: 15px;

                &:last-child {
                    margin-bottom: 0;
                }

                i {
                    display: inline-block;
                    color: $theme-color;
                    margin-right: 10px;
                    position: relative;
                    top: 1px;
                }

                color: #888;
            }
        }

        blockquote {
            position: relative;
            color: $white;
            font-weight: 400;
            clear: both;
            z-index: 1;
            margin: 40px 0;
            text-align: left;
            padding: 40px;
            background-color: $theme-color;
            border: none;
            border-radius: 0;
            overflow: hidden;

            .icon i {
                font-size: 30px;
                color: $white;
                display: block;
                margin-bottom: 20px;
            }

            h4 {
                font-weight: 500;
                font-size: 15px;
                line-height: 24px;
                color: $white;
            }

            span {
                font-size: 13px;
                display: block;
                margin-top: 20px;
                color: $white;
            }

        }


    }
}

.blog-single .post-thumbnils {
    position: relative;
    border-radius: 0;
    overflow: hidden;

    img {
        width: 100%;
    }

}


ul.custom-flex {
    list-style: none;
    padding: 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.popular-tag-widget .tag-title {
    margin-bottom: 20px;
    font-weight: 600;
    font-size: 16px;
}

.post-tags-media .share-title {
    margin-bottom: 20px;
    font-weight: 600;
    font-size: 16px;
}


.post-details .post-tags-media .post-social-media ul {
    justify-content: flex-end;

    li {
        margin-right: 7px;

        &:last-child {
            margin: 0;
        }

        a {
            font-size: 14px;
            color: $white;
            height: 35px;
            width: 35px;
            line-height: 35px;
            background-color: $theme-color;
            text-align: center;
            border-radius: 0;

            &.facebook {
                background-color: #3b5999;
            }

            &.twitter {
                background-color: #55acee;
            }

            &.google {
                background-color: #dd4b39;
            }

            &.pinterest {
                background-color: #bd081c;
            }

            &.vimeo {
                background-color: #1ab7ea;
            }

            &:hover {
                color: $white;
                background-color: $black;
            }
        }
    }
}

.post-details .post-tags-media {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
}

.post-details .post-meta li {
    font-weight: 500;
    margin-right: 25px;
}

.post-details .post-meta li a {
    font-size: 13px;
    font-weight: 400;
    font-weight: 500;
    color: $black;
}

.post-details .post-meta li a i {
    font-weight: 400;
    margin-right: 5px;
    color: $theme-color;
}

.post-details>p {
    font-size: 14px;
}

.post-details .post-image {
    margin: 40px 0;
    width: 100%;

}

.post-details .post-image img {
    width: 100%;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    transition: all 0.4s ease;
    border-radius: 0;
}

.post-details>ul>li {
    font-weight: 500;
}

.post-details .post-tags-media .post-tags .tags a {
    color: #333;
    background: transparent;
}

.post-details .post-tags-media .post-tags .tags a:hover {
    color: #fff;
    background-color: $theme-color;
}

.post-details .post-tags-media .post-social-media {
    text-align: right;
    position: relative;
    top: -5px;
}

.post-details .post-tags-media .post-social-media ul {
    justify-content: flex-end;
}

.detail-post-navigation {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    padding: 30px;
    border: 1px solid #eee;
}



/*comments*/
.post-comments {
    margin-top: 70px;
}

.comment-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    margin-bottom: 40px !important;
    padding-bottom: 20px;
    background: $gray;
    padding: 15px 20px;
    border-left: 4px solid $theme-color;
}

.comment-reply-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    margin-bottom: 40px !important;
    padding-bottom: 20px;
    background: $gray;
    padding: 15px 20px;
    border-left: 4px solid $theme-color;
}

.post-comments .comments-list li {
    padding-left: 130px;
    position: relative;
    font-size: 14px;
}

.post-comments .comments-list li .comment-img {
    position: absolute;
    left: 0;
    width: 100px;
    height: 100px;
}

.post-comments .comments-list li .comment-img img {
    max-width: 100px;
    max-height: 100px;
    border-radius: 50%;
}

.post-comments .comments-list li .comment-desc .desc-top {
    margin-bottom: 20px;
    position: relative;
    display: block;
}

.post-comments .comments-list li .comment-desc .desc-top h6 {
    font-size: 17px;
    margin-bottom: 8px;
    font-weight: 500;
}

.post-comments .comments-list li .comment-desc .desc-top h6 .saved {
    color: $theme-color;
    font-size: 14px;
    margin-left: 10px;
}

.post-comments .comments-list li .comment-desc .desc-top span.date {
    font-size: 12px;
    font-weight: 400;
}

.post-comments .comments-list li .comment-desc .desc-top .reply-link {
    position: absolute;
    right: 0;
    top: 0;
    display: inline-block;
    font-size: 12px;
    font-weight: 500;
    z-index: 2;
    color: $black;

    &:hover {
        color: $theme-color;
    }
}

.post-comments .comments-list li .comment-desc .desc-top .reply-link i {
    margin-right: 5px;
}

.post-comments .comments-list li .comment-desc p {
    font-weight: 400;
    margin-bottom: 0;
    font-size: 14px;
}

.post-comments .comments-list li.children {
    margin-left: 130px;
}

.post-comments .comments-list li:not(:first-child) {
    padding-top: 30px;
    margin-top: 30px;
    border-top: 1px solid #eee;
}

/*Comment form*/
.comment-form {
    margin-top: 70px;
}

.comment-form form .form-box {
    position: relative;
}

.comment-form form .form-box .icon {
    position: absolute;
    top: 17px;
    right: 25px;
    font-size: 16px;
}

.comment-form form .form-box .form-control-custom {
    border: none;
    background: #fff;
    font-size: 14px;
    color: $black;
    padding: 0 20px;
    font-weight: 500;
    height: 55px;
    border: 1px solid #eee;
    margin-bottom: 25px;
    font-weight: 400;
    border-radius: 0;
    outline: 0;
}

.comment-form form .form-box textarea.form-control-custom {
    height: 200px;
    padding: 25px;
    outline: 0;
}

.comment-form form .form-box .form-control-custom::placeholder {
    font-size: 14px;
    color: #333;
    font-weight: 400;
    padding: 25px;
    outline: 0;
}

/* News sidebar */
.sidebar .widget {
    padding: 40px;
    background-color: $white;
    margin-bottom: 30px;
    border-radius: 0;
    border: 1px solid #eee;

    &:last-child {
        margin-bottom: 0;
    }
}

.sidebar .widget .widget-title {
    font-size: 17px;
    font-weight: 600;
    color: #081828;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-left: 4px solid $theme-color;
    padding: 10px 10px 10px 16px;
    background: $gray;
}

.sidebar .widget.search-widget form {
    position: relative;
}

.sidebar .widget.search-widget form input {
    width: 100%;
    background-color: transparent;
    height: 55px;
    border: none;
    padding: 0 70px 0 20px;
    font-size: 14px;
    font-weight: 400;
    border: 1px solid #eee;
    border-radius: 0;
    background-color: $white;
    color: $black;
}

.sidebar .widget.search-widget form input::placeholder {
    color: #333;
}

.sidebar .widget.search-widget form button {
    position: absolute;
    right: 10px;
    top: 50%;
    width: 40px;
    height: 38px;
    z-index: 1;
    color: #fff !important;
    font-size: 13px;
    -webkit-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    color: #fff;
    border-radius: 0;
    padding: 0 !important;
    border: none;
    margin-top: -19px;
    background: $theme-color;

    &:hover {
        background-color: $black;
        color: $white;
    }
}


.sidebar .widget.popular-feeds .single-popular-feed {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 25px;
    padding-bottom: 25px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid #eee;

    &:last-child {
        border: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
}

.sidebar .widget.popular-feeds .single-popular-feed {
    position: relative;
    padding-left: 100px;

    .feed-img {
        overflow: hidden;
        width: 80px;
        border-radius: 50%;
        height: 80px;

        img {
            width: 80px;
            border-radius: 50%;
            height: 80px;
            overflow: hidden;
            display: block;
            transition: all 0.4s ease-in-out;
        }

        &:hover {
            img {
                transform: scale(1.1);
            }
        }

        position: absolute;
        left: 0;
        top: 0;
    }
}

.sidebar .widget.popular-feeds .single-popular-feed .feed-desc .post-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.5;
}

.sidebar .widget.popular-feeds .single-popular-feed .feed-desc .post-title a {
    font-size: 14px;
    font-weight: 500;

    &:hover {
        color: $theme-color;
    }
}


.sidebar .widget.popular-feeds .single-popular-feed .feed-desc .time {
    font-weight: 400;
    font-size: 12px;
}

.sidebar .widget.popular-feeds .single-popular-feed .feed-desc .time>i {
    margin-right: 4px;
}

.sidebar .widget.categories-widget ul li {
    margin-bottom: 25px;
    display: block;

    &:last-child {
        margin-bottom: 0;
    }
}


.sidebar .widget.categories-widget ul li a {
    background-color: $white;
    color: $black;
    font-weight: 500;
    display: block;
    border-radius: 0;
    position: relative;
    font-size: 14px;
    padding: 0;
    padding-right: 40px;

    &:hover {
        color: $theme-color;
    }

    span {
        float: right;
        margin: 0;
        height: 36px;
        width: 36px;
        line-height: 36px;
        font-size: 13px;
        font-weight: 500;
        color: #666;
        border: 1px solid #eee;
        border-radius: 50%;
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -18px;
        text-align: center;
    }
}


.sidebar .widget.categories-widget ul li a:hover span {
    background: $theme-color;
    color: #fff;
    border-color: transparent;
}

.sidebar .widget.popular-tag-widget {
    padding-bottom: 30px;
}

.popular-tag-widget .tags>a {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 8px 20px;
    text-transform: capitalize;
    font-size: 13px;
    font-weight: 500;
    background: $gray;
    margin-right: 7px;
    margin-bottom: 10px;
    color: $black;
    border: 1px solid #eee;
    border-radius: 0;
}

.popular-tag-widget .tags>a:hover {
    background-color: $theme-color;
    color: #fff;
    border-color: transparent;
}