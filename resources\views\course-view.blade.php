@extends('layouts.app')

@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ setting('site.title') }}</title>
    <meta name="description" content="{{ setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">

    {{-- <title>{{ $course->seo_title }}</title>
    <meta name="description" content="{{ $course->meta_description }}">
    <meta name="keywords" content="{{ $course->meta_keywords }}"> --}}
@endsection

@section('styles')

<style>
    .course-description {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
    }
</style>
@endsection

@section('content')
<div class="breadcrumbs overlay" style="background-image: url('{{ asset('storage/' . $course->image) }}') !important;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title">{{ $course->title }}</h1>
                    <p>{{ $course->excerpt }}</p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="{{ url('/') }}">Home</a></li>
                    <li>{{ $course->title }}</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<section class="course-details section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-7 col-12">
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                        <div class="course-overview">
                            <h3 class="title">About This Course</h3>
                            <p>{{ $course->description }}</p>
                            @if (!empty($course->video_url))
                            @php
                                // Extract the video ID from the URL
                                $url_parts = parse_url($course->video_url);
                                parse_str($url_parts['query'], $query_params);
                                $video_id = $query_params['v'] ?? null;
                                $embed_url = $video_id ? 'https://www.youtube.com/embed/' . $video_id : null;
                            @endphp

                            @if ($embed_url)
                                <div class="overview-course-video">
                                    <iframe src="{{ $embed_url }}" frameborder="0" allowfullscreen></iframe>
                                </div>
                            @else
                                <div class="overview-course-picture">
                                    <img src="{{ asset('storage/' . $course->image) }}" alt="Course Picture" style="width: 100%; height: auto;">
                                </div>
                            @endif
                            @else
                                <div class="overview-course-picture">
                                    <img src="{{ asset('storage/' . $course->image) }}" alt="Course Picture" style="width: 100%; height: auto;">
                                </div>
                            @endif
                            <p>{{ $course->additional_info }}</p>
                            <div class="bottom-content">
                                <div class="row align-items-center">
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="button">
                                            {{-- Uncomment this line if you have a route for buying the course --}}
                                            <a href="{{ route('trial-class') }}" class="btn">Buy this course</a>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <ul class="share">
                                            <li><span>Share this course:</span></li>
                                            <li><a href="https://www.facebook.com/sharer/sharer.php?u={{ url()->current() }}" target="_blank"><i class="lni lni-facebook-original"></i></a></li>
                                            <li><a href="https://twitter.com/intent/tweet?url={{ url()->current() }}" target="_blank"><i class="lni lni-twitter-original"></i></a></li>
                                            <li><a href="https://www.linkedin.com/sharing/share-offsite/?url={{ url()->current() }}" target="_blank"><i class="lni lni-linkedin-original"></i></a></li>
                                            <li><a href="mailto:?subject=Check out this course&body={{ url()->current() }}" target="_blank"><i class="lni lni-google"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Add other tabs here if necessary -->
                </div>
            </div>
            <!-- Start Course Sidebar -->
            <aside class="col-lg-4 col-md-5 col-12">
                <div class="course-sidebar">
                    <div class="sidebar mb-4">
                        <div class="widget categories-widget">
                            <h5 class="widget-title">Categories</h5>
                            <ul class="custom">
                                @foreach($categories as $category)
                                    <li>
                                        <a href="{{ route('category.filter.course', $category->slug) }}">{{ $category->title }} <span>{{ $category->courses_count }}</span></a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    <div class="sidebar-widget other-course-wedget">
                        <h3 class="sidebar-widget-title">Recent Courses</h3>
                        <div class="sidebar-widget-content">
                            <ul class="sidebar-widget-course list-unstyled">
                                @foreach($recentCourses as $recentCourse)
                                <li class="single-course mb-3 d-flex">
                                    <div class="thumbnail me-3">
                                        <a href="{{ route('courseShow', $recentCourse->slug) }}" class="image">
                                            <img src="{{ asset('storage/' . $recentCourse->image) }}" alt="Course Image" class="img-fluid">
                                        </a>
                                    </div>
                                    <div class="info">
                                        <span class="price">${{ $recentCourse->price }}<span>.00</span></span>
                                        <h6 class="title">
                                            <a href="{{ route('courseShow', $recentCourse->slug) }}">
                                                {{ $recentCourse->title }}
                                            </a>
                                        </h6>
                                        <p class="course-description">{{ $course->description }}</p>

                                    </div>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>

                </div>
            </aside>
            <!-- End Course Sidebar -->
        </div>
    </div>
</section>
@endsection
