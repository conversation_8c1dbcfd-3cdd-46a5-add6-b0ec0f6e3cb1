@extends('layouts.app')

@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ setting('site.title') }}</title>
    <meta name="description" content="{{ setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">

    {{-- <title>{{ $course->seo_title }}</title>
    <meta name="description" content="{{ $course->meta_description }}">
    <meta name="keywords" content="{{ $course->meta_keywords }}"> --}}
@endsection

@section('styles')

<style>
    .course-description {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
    }

    /* Course Reviews Styles */
    .course-reviews {
        border-top: 1px solid #eee;
        padding-top: 30px;
    }

    .course-reviews .title {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
    }

    .reviews-summary {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
    }

    .average-rating {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .rating-number {
        font-size: 2rem;
        font-weight: bold;
        color: #F6B500;
    }

    .stars i {
        color: #F6B500;
        font-size: 18px;
    }

    .total-reviews {
        color: #666;
        font-size: 14px;
    }

    .single-review {
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }

    .reviewer-name {
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .review-rating i {
        color: #F6B500;
        font-size: 14px;
    }

    .review-date {
        color: #999;
        font-size: 13px;
    }

    .review-content p {
        margin-bottom: 0;
        color: #666;
        line-height: 1.6;
    }

    .no-reviews {
        text-align: center;
        color: #999;
        font-style: italic;
        padding: 40px 0;
    }

    .add-review-form {
        background: #f8f9fa;
        padding: 30px;
        border-radius: 8px;
    }

    .add-review-form h4 {
        margin-bottom: 20px;
        color: #333;
    }

    .star-rating {
        display: flex;
        gap: 5px;
        margin-top: 5px;
    }

    .star-rating .star {
        font-size: 24px;
        color: #ddd;
        cursor: pointer;
        transition: color 0.2s;
    }

    .star-rating .star:hover,
    .star-rating .star.hover {
        color: #F6B500;
    }

    .star-rating .star.active {
        color: #F6B500;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
        color: #333;
    }

    .form-control {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px 15px;
        font-size: 14px;
    }

    .form-control:focus {
        border-color: #F6B500;
        box-shadow: 0 0 0 0.2rem rgba(246, 181, 0, 0.25);
    }

    .text-danger {
        font-size: 12px;
        margin-top: 5px;
        display: block;
    }
</style>
@endsection

@section('content')
<div class="breadcrumbs overlay" style="background-image: url('{{ asset('storage/' . $course->image) }}') !important;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title">{{ $course->title }}</h1>
                    <p>{{ $course->excerpt }}</p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="{{ url('/') }}">Home</a></li>
                    <li>{{ $course->title }}</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<section class="course-details section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-7 col-12">
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                        <div class="course-overview">
                            <h3 class="title">About This Course</h3>
                            <p>{{ $course->description }}</p>
                            @if (!empty($course->video_url))
                            @php
                                // Extract the video ID from the URL
                                $url_parts = parse_url($course->video_url);
                                parse_str($url_parts['query'], $query_params);
                                $video_id = $query_params['v'] ?? null;
                                $embed_url = $video_id ? 'https://www.youtube.com/embed/' . $video_id : null;
                            @endphp

                            @if ($embed_url)
                                <div class="overview-course-video">
                                    <iframe src="{{ $embed_url }}" frameborder="0" allowfullscreen></iframe>
                                </div>
                            @else
                                <div class="overview-course-picture">
                                    <img src="{{ asset('storage/' . $course->image) }}" alt="Course Picture" style="width: 100%; height: auto;">
                                </div>
                            @endif
                            @else
                                <div class="overview-course-picture">
                                    <img src="{{ asset('storage/' . $course->image) }}" alt="Course Picture" style="width: 100%; height: auto;">
                                </div>
                            @endif
                            <p>{{ $course->additional_info }}</p>
                            <div class="bottom-content">
                                <div class="row align-items-center">
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="button">
                                            {{-- Uncomment this line if you have a route for buying the course --}}
                                            <a href="{{ route('trial-class') }}" class="btn">Buy this course</a>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <ul class="share">
                                            <li><span>Share this course:</span></li>
                                            <li><a href="https://www.facebook.com/sharer/sharer.php?u={{ url()->current() }}" target="_blank"><i class="lni lni-facebook-original"></i></a></li>
                                            <li><a href="https://twitter.com/intent/tweet?url={{ url()->current() }}" target="_blank"><i class="lni lni-twitter-original"></i></a></li>
                                            <li><a href="https://www.linkedin.com/sharing/share-offsite/?url={{ url()->current() }}" target="_blank"><i class="lni lni-linkedin-original"></i></a></li>
                                            <li><a href="mailto:?subject=Check out this course&body={{ url()->current() }}" target="_blank"><i class="lni lni-google"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Add other tabs here if necessary -->
                </div>

                <!-- Course Reviews Section -->
                <div class="course-reviews mt-5">
                    <h3 class="title">Course Reviews</h3>

                    @if($reviews->count() > 0)
                        <div class="reviews-summary mb-4">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="rating-overview">
                                        <div class="average-rating">
                                            <span class="rating-number">{{ $course->average_rating }}</span>
                                            <div class="stars">
                                                {!! $course->star_rating !!}
                                            </div>
                                            <span class="total-reviews">({{ $course->reviews_count }} {{ $course->reviews_count == 1 ? 'Review' : 'Reviews' }})</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="reviews-list">
                            @foreach($reviews as $review)
                                <div class="single-review mb-4">
                                    <div class="review-header d-flex justify-content-between align-items-start">
                                        <div class="reviewer-info">
                                            <h6 class="reviewer-name">{{ $review->user_name }}</h6>
                                            <div class="review-rating">
                                                {!! $review->star_rating !!}
                                            </div>
                                        </div>
                                        <span class="review-date">{{ $review->created_at->format('M d, Y') }}</span>
                                    </div>
                                    <div class="review-content mt-2">
                                        <p>{{ $review->comment }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="no-reviews">No reviews yet. Be the first to review this course!</p>
                    @endif

                    <!-- Add Review Form -->
                    <div class="add-review-form mt-5">
                        <h4>Add Your Review</h4>
                        <form action="{{ route('course-reviews.store') }}" method="POST">
                            @csrf
                            <input type="hidden" name="course_id" value="{{ $course->id }}">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="user_name">Your Name *</label>
                                        <input type="text" name="user_name" id="user_name" class="form-control" required value="{{ old('user_name') }}">
                                        @error('user_name')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="user_email">Your Email *</label>
                                        <input type="email" name="user_email" id="user_email" class="form-control" required value="{{ old('user_email') }}">
                                        @error('user_email')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="rating">Your Rating *</label>
                                <div class="rating-input">
                                    <input type="hidden" name="rating" id="rating" value="{{ old('rating', 5) }}">
                                    <div class="star-rating">
                                        <span class="star" data-rating="1">★</span>
                                        <span class="star" data-rating="2">★</span>
                                        <span class="star" data-rating="3">★</span>
                                        <span class="star" data-rating="4">★</span>
                                        <span class="star" data-rating="5">★</span>
                                    </div>
                                </div>
                                @error('rating')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="comment">Your Review *</label>
                                <textarea name="comment" id="comment" class="form-control" rows="5" required placeholder="Share your experience with this course...">{{ old('comment') }}</textarea>
                                @error('comment')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>

                            <button type="submit" class="btn btn-primary">Submit Review</button>
                        </form>
                    </div>
                </div>
            </div>
            <!-- Start Course Sidebar -->
            <aside class="col-lg-4 col-md-5 col-12">
                <div class="course-sidebar">
                    <div class="sidebar mb-4">
                        <div class="widget categories-widget">
                            <h5 class="widget-title">Categories</h5>
                            <ul class="custom">
                                @foreach($categories as $category)
                                    <li>
                                        <a href="{{ route('category.filter.course', $category->slug) }}">{{ $category->title }} <span>{{ $category->courses_count }}</span></a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    <div class="sidebar-widget other-course-wedget">
                        <h3 class="sidebar-widget-title">Recent Courses</h3>
                        <div class="sidebar-widget-content">
                            <ul class="sidebar-widget-course list-unstyled">
                                @foreach($recentCourses as $recentCourse)
                                <li class="single-course mb-3 d-flex">
                                    <div class="thumbnail me-3">
                                        <a href="{{ route('courseShow', $recentCourse->slug) }}" class="image">
                                            <img src="{{ asset('storage/' . $recentCourse->image) }}" alt="Course Image" class="img-fluid">
                                        </a>
                                    </div>
                                    <div class="info">
                                        <span class="price">${{ $recentCourse->price }}<span>.00</span></span>
                                        <h6 class="title">
                                            <a href="{{ route('courseShow', $recentCourse->slug) }}">
                                                {{ $recentCourse->title }}
                                            </a>
                                        </h6>
                                        <p class="course-description">{{ $course->description }}</p>

                                    </div>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>

                </div>
            </aside>
            <!-- End Course Sidebar -->
        </div>
    </div>
</section>

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const stars = document.querySelectorAll('.star-rating .star');
    const ratingInput = document.getElementById('rating');

    stars.forEach(star => {
        star.addEventListener('click', function() {
            const rating = this.getAttribute('data-rating');
            ratingInput.value = rating;

            // Update visual state
            stars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });

        star.addEventListener('mouseover', function() {
            const rating = this.getAttribute('data-rating');
            stars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('hover');
                } else {
                    s.classList.remove('hover');
                }
            });
        });
    });

    // Initialize with default rating
    const defaultRating = ratingInput.value;
    stars.forEach((s, index) => {
        if (index < defaultRating) {
            s.classList.add('active');
        }
    });

    document.querySelector('.star-rating').addEventListener('mouseleave', function() {
        stars.forEach(s => s.classList.remove('hover'));
    });
});
</script>
@endsection

@endsection
