<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Testimonial extends Model
{
    use HasFactory;
    protected $table = 'testimonial';
    protected $fillable = [
        'name',
        'image',
        'comment',
        'country',
        'is_approved',
        'is_read'
    ];

    protected $casts = [
        'is_approved' => 'boolean',
        'is_read' => 'boolean'
    ];

    // Scope for approved testimonials only
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    // Scope for pending testimonials
    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }

    // Scope for unread testimonials
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }
}
