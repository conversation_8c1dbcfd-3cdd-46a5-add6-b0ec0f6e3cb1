<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    protected $table = 'courses';

    public function category()
    {
        return $this->belongsTo(CoursesCategory::class, 'category_id');
    }

    // Relationship with CourseReview
    public function reviews()
    {
        return $this->hasMany(CourseReview::class);
    }

    // Get only approved reviews
    public function approvedReviews()
    {
        return $this->hasMany(CourseReview::class)->approved();
    }

    // Get average rating
    public function getAverageRatingAttribute()
    {
        $average = $this->approvedReviews()->avg('rating');
        return $average ? round($average, 1) : 0;
    }

    // Get total reviews count
    public function getReviewsCountAttribute()
    {
        return $this->approvedReviews()->count();
    }

    // Get star rating as HTML
    public function getStarRatingAttribute()
    {
        $rating = $this->average_rating;
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $rating) {
                $stars .= '<i class="lni lni-star-filled"></i>';
            } else {
                $stars .= '<i class="lni lni-star"></i>';
            }
        }
        return $stars;
    }

    // Check if course has reviews
    public function hasReviews()
    {
        return $this->reviews_count > 0;
    }
}
