<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Contact extends Model
{
    protected $table = 'contact';
    protected $fillable = [
        'name',
        'phone',
        'email',
        'subject',
        'message',
        'is_approved',
        'is_read'
    ];

    protected $casts = [
        'is_approved' => 'boolean',
        'is_read' => 'boolean'
    ];

    // Scope for approved contacts only
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    // Scope for pending contacts
    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }

    // Scope for unread contacts
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }
}
