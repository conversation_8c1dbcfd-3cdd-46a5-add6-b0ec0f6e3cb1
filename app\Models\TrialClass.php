<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrialClass extends Model
{
    use HasFactory;
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'country',
        'choose_course',
        'notes',
        'is_read',
        'is_approved',
        'state',
    ];

    protected $table = 'trial_class';

    protected $casts = [
        'is_read' => 'boolean',
        'is_approved' => 'boolean'
    ];

    protected $attributes = [
        'is_read' => false,
        'is_approved' => false,
    ];

    // Scope for approved trial classes only
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    // Scope for pending trial classes
    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }

    // Scope for unread trial classes
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }
}
