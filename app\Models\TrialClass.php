<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrialClass extends Model
{
    use HasFactory;
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'country',
        'choose_course',
        'notes',
        'is_read',
        'state',
    ];

    protected $table = 'trial_class';

    protected $attributes = [
        'is_read' => false,
    ];
}
