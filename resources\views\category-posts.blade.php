@extends('layouts.app')

@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ $category->name }} - {{ setting('site.title') }}</title>
    <meta name="description" content="{{ $category->description ?? setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">
@endsection

@section('content')
    <!-- Blog Page Content -->
    <section class="section latest-news-area blog-grid-page">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 col-md-7 col-12">
                    <div class="row">
                        @foreach($posts as $post)
                            <div class="col-lg-6 col-12">
                                <!-- Single News -->
                                <div class="single-news custom-shadow-hover wow fadeInUp" data-wow-delay=".4s">
                                    <div class="image">
                                        <a href="{{ route('postShow', $post->slug) }}">
                                            <img class="thumb" src="{{ Voyager::image($post->image) }}" alt="{{ $post->title }}">
                                        </a>
                                    </div>
                                    <div class="content-body">
                                        <div class="meta-data">
                                            <ul>
                                                <li>
                                                    <i class="lni lni-tag"></i>
                                                    <a href="javascript:void(0)">{{ $post->category->name ?? 'Uncategorized' }}</a>
                                                </li>
                                                <li>
                                                    <i class="lni lni-calendar"></i>
                                                    <a href="javascript:void(0)">{{ $post->created_at->format('F d, Y') }}</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <h4 class="title"><a href="{{ route('postShow', $post->slug) }}">{{ $post->title }}</a></h4>
                                        <p>{{ Str::limit($post->excerpt, 100) }}</p>
                                        <div class="button">
                                            <a href="{{ route('postShow', $post->slug) }}" class="btn">Read More</a>
                                        </div>
                                    </div>
                                </div>
                                <!-- End Single News -->
                            </div>
                        @endforeach
                    </div>
                    <!-- Pagination -->
                    <div class="pagination center">
                        {{ $posts->links() }}
                    </div>
                    <!--/ End Pagination -->
                </div>
            <!-- Sidebar -->
            <aside class="col-lg-4 col-md-5 col-12">
                <div class="sidebar">
                    <!-- Search Widget -->
                    {{-- <div class="widget search-widget">
                        <h5 class="widget-title">Search Here</h5>
                        <form id="searchForm">
                            <input id="searchInput" type="text" placeholder="Search Here...">
                            <button type="submit"><i class="lni lni-search-alt"></i></button>
                        </form>
                    </div> --}}
                    <!-- Recent Posts Widget -->
                    <div class="widget popular-feeds">
                        <h5 class="widget-title">Recent Posts</h5>
                        <div class="popular-feed-loop">
                            @foreach($recentPosts as $recentPost)
                                <div class="single-popular-feed">
                                    <div class="feed-img">
                                        <a href="{{ route('postShow', $recentPost->slug) }}">
                                            <img src="{{ Voyager::image($recentPost->image) ?? 'https://via.placeholder.com/300x300' }}" alt="{{ $recentPost->title }}">
                                        </a>
                                    </div>
                                    <div class="feed-desc">
                                        <h6 class="post-title"><a href="{{ route('postShow', $recentPost->slug) }}">{{ $recentPost->title }}</a></h6>
                                        <span class="time"><i class="lni lni-calendar"></i> {{ $recentPost->created_at->format('d M Y') }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <!-- Categories Widget -->
                    <div class="widget categories-widget">
                        <h5 class="widget-title">Categories</h5>
                        <ul class="custom">
                            @foreach($categories as $category)
                                <li>
                                    <a href="{{ route('category.filter', $category->slug) }}">{{ $category->name }} <span>{{ $category->posts_count }}</span></a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </aside>
            <!-- End Sidebar -->
            </div>
        </div>
    </section>
    <!-- End Blog Page Content -->
@endsection
