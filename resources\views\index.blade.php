@extends('layouts.app')
@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ setting('site.title') }}</title>
    <meta name="description" content="{{ setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
@endsection

@section('styles')
<style>
    .course-description {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
}
.features .single-feature {
    padding: 20px;
    text-align: center;
    background: #fff;
    border: 1px solid #eee;
    margin: 10px;
    transition: all 0.3s ease;
}

.features .single-feature .icon {
    font-size: 50px;
    color: #0EDC8D;
    margin-bottom: 20px;
}

.features .single-feature h3 {
    font-size: 24px;
    margin-bottom: 15px;
}

.features .single-feature p {
    font-size: 16px;
    color: #777;
    margin-bottom: 20px;
}

.features .single-feature:hover {
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
}



.single-testimonial .text {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center; /* Center the text horizontally */
    text-align: center; /* Center the text within the div */
    height: 70%;
}
.modal-content {
    border-radius: 10px;
    padding: 20px;
}

.modal-header {
    border-bottom: none;
}

.modal-title {
    font-weight: bold;
}

.modal-body {
    padding: 20px;
}

.mobile-menu-btn {
    padding: 0px !important;
    margin-top: -80px !important;
    margin-right: -496px !important;
}

.hero-area.style3 .hero-inner .hero-text {
    margin-top: 65px !important;
    text-align: left;
}
@media (max-width: 768px) {
    #introVideo {
        height: 100% !important;  /* تقليل ارتفاع الفيديو على الشاشات الصغيرة */
        width: 100% !important;
    }
}

/* Course Actions and Modal Styles */
.course-actions {
    text-align: center;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.add-review-btn {
    border: 1px solid #F6B500;
    color: #F6B500;
    padding: 8px 16px;
    border-radius: 5px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.add-review-btn:hover {
    background: #F6B500;
    color: white;
    border-color: #F6B500;
}

.add-review-btn i {
    margin-right: 5px;
}

/* Modal Styles */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #F6B500 0%, #e6a500 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
}

.btn-close {
    filter: brightness(0) invert(1);
}

.course-info h6 {
    color: #333;
    font-weight: 600;
    font-size: 16px;
}

#modal_star_rating {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 8px;
}

#modal_star_rating .star {
    font-size: 24px;
    color: #ddd;
    cursor: pointer;
    transition: all 0.2s ease;
}

#modal_star_rating .star:hover,
#modal_star_rating .star.hover {
    color: #F6B500;
    transform: scale(1.1);
}

#modal_star_rating .star.active {
    color: #F6B500;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #F6B500;
    box-shadow: 0 0 0 0.2rem rgba(246, 181, 0, 0.15);
}

.modal-footer .btn-primary {
    background: #F6B500;
    border-color: #F6B500;
    padding: 10px 25px;
    font-weight: 600;
}

.modal-footer .btn-primary:hover {
    background: #e6a500;
    border-color: #e6a500;
}

</style>
@endsection

@section('content')
<!-- Start Hero Area -->
<section class="hero-area">
    <div class="hero-slider">
        <!-- Single Slider -->
        <div class="hero-inner overlay" style="background-image: url('{{ asset('assets/images/koran_3.jpg') }}');">
            <div class="container">
                <div class="row ">
                    <div class="col-lg-8 offset-lg-2 col-md-12 co-12">
                        <div class="home-slider">
                            <div class="hero-text">
                                <h5 class="wow fadeInUp" data-wow-delay=".3s">Start to Learning Today</h5>
                                <h1 class="wow fadeInUp" data-wow-delay=".5s">Hurry up to join us on board of <br> Sabeel Ul-Quraan</h1>
                                <p class="wow fadeInUp" data-wow-delay=".7s">Sabeel Ul- Quraan isn't merely an online islamic academy that provides
                                    <br> islamic studies. It is an entity that comes up Muslims standard
                                    <br>earthly to enable them win jannah afterlife.</p>
                                <div class="button wow fadeInUp" data-wow-delay=".9s">
                                    <a href="{{ route('about') }}" class="btn">Learn More</a>
                                    <a href="{{ route('courses') }}" class="btn alt-btn">Our Courses</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--/ End Single Slider -->
        <!-- Single Slider -->
        <div class="hero-inner overlay" style="background-image: url('{{ asset('assets/images/452096542-10.jpg') }}');">
            <div class="container">
                <div class="row ">
                    <div class="col-lg-8 offset-lg-2 col-md-12 co-12">
                        <div class="home-slider">
                            <div class="hero-text">
                                <h5 class="wow fadeInUp" data-wow-delay=".3s">Start to learning Today</h5>
                                <h1 class="wow fadeInUp" data-wow-delay=".5s">Knock the door of Sabeel Ul-Quraan<br>to get a board islamic insight </h1>
                                <p class="wow fadeInUp" data-wow-delay=".7s">If you want to educate yourself and receive
                                    <br> islamic perception, just delve into
                                    <br>Sabeel Ul- Ouraan academy.</p>
                                <div class="button wow fadeInUp" data-wow-delay=".9s">
                                    <a href="{{ route('about') }}" class="btn">Learn More</a>
                                    <a href="{{ route('courses') }}" class="btn alt-btn">Our Courses</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--/ End Single Slider -->
        <!-- Single Slider -->
        <div class="hero-inner overlay" style="background-image: url('{{ asset('assets/images/1666958946_20-celes-club-p-kuran-oboi-krasivo-21.jpg') }}');">
            <div class="container">
                <div class="row ">
                    <div class="col-lg-8 offset-lg-2 col-md-12 co-12">
                        <div class="home-slider">
                            <div class="hero-text">
                                <h5 class="wow fadeInUp" data-wow-delay=".3s">Start to learning Today</h5>
                                <h1 class="wow fadeInUp" data-wow-delay=".5s">Your Ideas Will Be <br> Heard & Supported</h1>
                                <p class="wow fadeInUp" data-wow-delay=".7s">Lorem Ipsum is simply dummy text of the
                                    printing and typesetting <br> industry. Lorem Ipsum has been the industry's
                                    standard
                                    <br>dummy text ever since an to impression.</p>
                                <div class="button wow fadeInUp" data-wow-delay=".9s">
                                    <a href="{{ route('about') }}" class="btn">Learn More</a>
                                    <a href="{{ route('courses') }}" class="btn alt-btn">Our Courses</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--/ End Single Slider -->
    </div>
</section>
<!--/ End Hero Area -->



<!-- Start Features Area -->
{{-- <section class="features">
    <div class="container-fluid">
        <div class="single-head">
            <div class="row">
                <div class="col-lg-4 col-md-4 col-12 mb-2 padding-zero">
                    <!-- Start Single Feature -->
                    <div class="single-feature">
                        <div class="icon">
                            <i class="lni lni-library"></i>
                        </div>
                        <h3><a href="{{ route('courses') }}">Trending Courses</a></h3>
                        <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Repellendus, quaerat beatae nulla debitis vitae temporibus sed.</p>
                        <div class="button">
                            <a href="{{ route('courses') }}" class="btn">Explore <i class="lni lni-arrow-right"></i></a>
                        </div>
                    </div>
                    <!-- End Single Feature -->
                </div>
                <div class="col-lg-4 col-md-4 col-12 mb-2 padding-zero">
                    <!-- Start Single Feature -->
                    <div class="single-feature">
                        <div class="icon">
                            <i class="lni lni-graduation"></i>
                        </div>
                        <h3><a href="{{ route('team') }}">Certified Teachers</a></h3>
                        <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Repellendus, quaerat beatae nulla debitis vitae temporibus sed.</p>
                        <div class="button">
                            <a href="{{ route('team') }}" class="btn">Explore <i class="lni lni-arrow-right"></i></a>
                        </div>
                    </div>
                    <!-- End Single Feature -->
                </div>
                <div class="col-lg-4 col-md-4 col-12 mb-2 padding-zero">
                    <!-- Start Single Feature -->
                    <div class="single-feature last">
                        <div class="icon">
                            <i class="lni lni-library"></i>
                        </div>
                        <h3><a href="{{ route('courses') }}">Books & Library</a></h3>
                        <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Repellendus, quaerat beatae nulla debitis vitae temporibus sed.</p>
                        <div class="button">
                            <a href="{{ route('courses') }}" class="btn">Explore <i class="lni lni-arrow-right"></i></a>
                        </div>
                    </div>
                    <!-- End Single Feature -->
                </div>
            </div>
        </div>
    </div>
</section> --}}

<!-- /End Features Area -->

<!-- Start About Us Area -->
<section class="about-us section  ">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 col-12">
                <div class="about-left">
                    <div class="about-title align-left">
                        <span class="wow fadeInDown" data-wow-delay=".2s">About Us</span>
                        <h2 class="wow fadeInUp" data-wow-delay=".4s">Welcome to our campus</h2>
                        <p class="wow fadeInUp" data-wow-delay=".6s">Catch your dream and delve with us into Sabeel Ul- Quran academy to achieve it.
                        Sabeel Ul- Quran is an online academy that consists of an elite team of
                        Al- Azhar Al- Sharif specialists who spread the awareness of the Islamic
                        culture and Quran for non- Arabs regardless of their ages, culture,
                        language and regions. Our academy is launched for Muslims who are
                        ready for improving the concept of Islamic science and expanding the
                        knowledge of Quran recitation and Memorization according to tajweed
                        rules. Our special courses include Noor El- Bayan, Quran memorization
                        and recitation with tajweed, Islamic studies, Ijazah and Qira'at.
                        Moreover, we highlight the significance of dependence on Allah
                        regarding all aspects of life and the afterlife.
                        </p>
                        <p class="qote wow fadeInUp" data-wow-delay=".8s">We also promote the inner
                        conscience which feeds on individual's faith according to creedal Islamic
                        culture. Furthermore, we provide senses of ease and flexibility through
                        learning journey thanks to the quality of teaching. We also provide
                        creative educational techniques of Quran memorization remotely, helping
                        learners get effective outcomes. Some of these techniques are shown
                        below:
                        - Online educational platforms and applications: These tools make
                        student access the lessons at any time or place easily, enabling them to
                        manage their time.
                        - Interactive lessons and virtual classes: This approach enables students to
                        communicate with their instructors directly and participate in
                        discussions.
                        - Personal following and evaluation: This style provides students with
                        notes regarding their level, helping them achieve continuous progress in
                        Quran memorization.
                        - Technical support: We provide learners with technical support team.
                        This style helps them in case of facing any technical issues.</p>
                        <div class="button wow fadeInUp" data-wow-delay="1s">
                            <a href="{{ route('trial-class') }}"  class="btn mb-2">Trial class</a>
                            <a href="https://www.youtube.com/watch?v=2MvQDXNwreI"
                                class="btn mb-2" target="_blank"> Play Video<i class="lni lni-play"></i></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-12">
                <div class="about-right wow fadeInRight" data-wow-delay=".4s">
                    <img  src="{{ asset('assets/images/quran-memorization-for-kids.jpg') }}" alt="#">
                </div>
            </div>
        </div>
        <!-- Start Mission & Vision Section -->
        <div class="row mt-5">
            <div class="col-lg-6 col-12 mb-4">
                <div class="about-title align-left">
                    <h2 class="wow fadeInUp mb-2" data-wow-delay=".4s">Our Mission</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Our mission is to provide high-quality education that transforms students into responsible, educated members of society. We aim to foster an environment that encourages critical thinking, innovation, and excellence in all areas of learning.</p>
                </div>
            </div>
            <div class="col-lg-6 col-12">
                <div class="about-title align-left">
                    <h2 class="wow fadeInUp mb-2" data-wow-delay=".4s">Our Vision</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Our vision is to be a leading educational institution that is recognized globally for its outstanding academic programs and contributions to research and community service. We strive to inspire and empower our students to achieve their full potential.</p>
                </div>
            </div>
        </div>
        <!-- End Mission & Vision Section -->
    </div>
</section>
<!-- /End About Us Area -->

 <!-- Start Hero Area -->
 <section class="hero-area style3">
    <!-- Single Slider -->
    <div class="hero-inner overlay" style="background-image: url('{{ asset('assets/images/1666958946_20-celes-club-p-kuran-oboi-krasivo-21.jpg') }}');">
        <div class="container">
            <div class="inner-content">
                <div class="row">
                    <div class="col-lg-6 col-12">
                        <div class="hero-text">
                            <h5 class="wow fadeInLeft" data-wow-delay=".3s">Welcome to Sabeel ulquraan!</h5>
                            <h1 class="wow fadeInLeft" data-wow-delay=".5s">Get ready to transform your future by learning the Quran <span>and unlocking your potential</span> with expert instructors.</h1>
                            <p class="wow fadeInLeft" data-wow-delay=".7s">At Sabeel ulquraan, we offer you the perfect opportunity to book personalized Quran classes with highly skilled trainers. Enhance your knowledge and deepen your understanding of the Quran in an engaging and rewarding way.</p>
                            <div class="button style2 wow fadeInLeft" data-wow-delay=".9s">
                                <a href="{{ route('trial-class') }}" class="btn">Book Now and Start Your Journey!</a>
                                <a href="https://www.youtube.com/watch?v=2MvQDXNwreI" class=" video-button" target="_blank"><i class="lni lni-play"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-12">
                        <div class="video-wrapper position-relative d-inline-block" style="max-width: 100%; margin-top: 65px;">
                            <video id="introVideo" class="video rounded shadow" poster="{{ asset('assets/images/Sabeel Ul-Quraan.png') }}" style="width: 100%; height: 600px; max-height: 500px; display: block; object-fit: cover;" preload="metadata" controls aria-label="Sabeel Ul-Quraan Introduction Video">
                                <source src="{{ asset('assets/images/Sabeel Ul-Quraan.mp4') }}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--/ End Single Slider -->
</section>



<!--/ End Hero Area -->

<!-- Start Courses Area-->
<section class="courses section grid-page">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title">
                    <div class="section-icon wow zoomIn" data-wow-delay=".4s">
                        <i class="lni lni-graduation"></i>
                    </div>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Featured Courses</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">There are many variations of passages of Lorem
                        Ipsum available, but the majority have suffered alteration in some form.</p>
                </div>
            </div>
                @foreach($courses as $index => $course)
                    @if($index % 3 == 0)
                        </div><div class="row">
                    @endif
                    <!-- Start Single Course -->
                    <div class="col-lg-4 col-md-6 col-12">
                        <div class="single-course wow fadeInUp" data-wow-delay=".2s">
                            <div class="course-image">
                                <a href="{{ route('courseShow', Str::slug($course->title)) }}">
                                    <img src="{{ asset('storage/'. $course->image) }}" alt="{{ $course->title }}">
                                </a>
                                <p class="price">${{ $course->price }}</p>
                            </div>
                            <div class="content">
                                <h3><a href="{{ route('courseShow', Str::slug($course->title)) }}">{{ $course->title }}</a></h3>
                                <p class="course-description">{{ $course->description }}</p>
                            </div>
                            <div class="bottom-content">
                                <ul class="review">
                                    @if($course->hasReviews())
                                        {!! $course->star_rating !!}
                                        <li>{{ $course->reviews_count }} {{ $course->reviews_count == 1 ? 'Review' : 'Reviews' }}</li>
                                    @else
                                        <li><i class="lni lni-star"></i></li>
                                        <li><i class="lni lni-star"></i></li>
                                        <li><i class="lni lni-star"></i></li>
                                        <li><i class="lni lni-star"></i></li>
                                        <li><i class="lni lni-star"></i></li>
                                        <li>No Reviews Yet</li>
                                    @endif
                                </ul>
                                <span class="tag">
                                    <i class="lni lni-tag"></i>
                                    <a href="{{ route('category.filter.course', $course->category->slug) }}">{{ $course->category->title }}</a>
                                </span>
                            </div>
                            <div class="course-actions mt-3">
                                <button type="button" class="btn btn-sm btn-outline-primary add-review-btn"
                                        data-course-id="{{ $course->id }}"
                                        data-course-title="{{ $course->title }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#reviewModal">
                                    <i class="lni lni-star"></i> Add Review
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- End Single Course -->
            @endforeach
        </div>
        <div class="row">
            <div class="col-12">
                <!-- Pagination -->
                <div class="pagination center">
                    <div class="button">
                        <a href="{{ route('courses') }}" class="btn">browsing all courses</a>
                    </div>
                </div>
                <!--/ End Pagination -->
            </div>
        </div>
    </div>
</section>
<!-- End Courses Area-->

<!-- Start Achivement Area -->
<section class="our-achievement section overlay"style="background-image: url('{{ asset('assets/images/koran_3.jpg') }}');">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-3 col-12">
                <div class="single-achievement wow fadeInUp" data-wow-delay=".2s">
                    <h3 class="counter"><span class="countup" data-end="580">0</span>+</h3>
                    <h4>Happy Clients</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-12">
                <div class="single-achievement wow fadeInUp" data-wow-delay=".4s">
                    <h3 class="counter"><span   class="countup" data-end="450">0</span>+</h3>
                    <h4>Online Courses</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-12">
                <div class="single-achievement wow fadeInUp" data-wow-delay=".6s">
                    <h3 class="counter"><span  class="countup" data-end="100">0</span>%</h3>
                    <h4>Satisfaction</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-12">
                <div class="single-achievement wow fadeInUp" data-wow-delay=".6s">
                    <h3 class="counter"><span   class="countup" data-end="100">0</span>%</h3>
                    <h4>Support</h4>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Achivement Area -->



<!-- Start Teachers -->
<section id="teachers" class="teachers section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center gray-bg">
                    <div class="section-icon wow zoomIn" data-wow-delay=".4s">
                        <i class="lni lni-users"></i>
                    </div>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Our Experienced Advisors</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">There are many variations of passages of Lorem
                        Ipsum available, but the majority have suffered alteration in some form.</p>
                </div>
            </div>
        </div>
        <div class="row">
            <!-- Your Blade template file -->
            @foreach($teams as $team)
            <!-- Single Team -->
            <div class="col-lg-6 col-md-6 col-12">
                <div class="single-team wow fadeInUp" data-wow-delay=".2s">
                    <div class="row">
                        <div class="col-lg-5 col-12">
                            <!-- Image -->
                            <div class="image">
                                <img src="{{ asset('storage/' . $team->image) }}" alt="{{ $team->name }}">
                            </div>
                            <!-- End Image -->
                        </div>
                        <div class="col-lg-7 col-12">
                            <div class="info-head">
                                <!-- Info Box -->
                                <div class="info-box">
                                    <span class="designation">{{ $team->designation }}</span>
                                    <h4 class="name"><a href="#">{{ $team->name }}</a></h4>
                                    <p>{{ $team->description }}</p>
                                </div>
                                <!-- End Info Box -->
                                <!-- Social -->
                                <ul class="social">
                                    <li><a href="{{ $team->facebook_link }}"><i class="lni lni-facebook-filled"></i></a></li>
                                    <li><a href="{{ $team->twitter_link }}"><i class="lni lni-twitter-original"></i></a></li>
                                    <li><a href="{{ $team->instagram_link }}"><i class="lni lni-instagram-original"></i></a></li>
                                    <li><a href="{{ $team->linkedin_link }}"><i class="lni lni-linkedin-original"></i></a></li>
                                </ul>
                                <!-- End Social -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Single Team -->
            @endforeach
        </div>
    </div>
</section>
<!--/ End Teachers Area -->

<!-- Start Testimonials Area -->
<section class="testimonials section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center gray-bg">
                    <div class="section-icon wow zoomIn" data-wow-delay=".4s">
                        <i class="lni lni-quotation"></i>
                    </div>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">What Our Students Say</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">There are many variations of passages of Lorem
                        Ipsum available, but the majority have suffered alteration in some form.</p>
                </div>
            </div>
        </div>
        <div class="row testimonial-slider">
            @foreach ($testimonials as $testimonial)
            <div class="col-lg-4 col-md-6 col-12 d-flex align-items-stretch">
                <!-- Start Single Testimonial -->
                <div class="single-testimonial">
                    <div class="text">
                        <p>{{ $testimonial->comment }}</p>
                    </div>
                    <div class="author">
                        <img src="{{ asset('storage/' . $testimonial->image) }}" alt="{{ $testimonial->name }}">
                        <h4 class="name">
                            {{ $testimonial->name }}
                            <span class="deg">{{ $testimonial->country }}</span>
                        </h4>
                    </div>
                </div>

                <!-- End Single Testimonial -->
            </div>
            @endforeach

        </div>
        <div class="row">
            <div class="col-12 text-center mt-4 button">
                <button type="button" class="btn" data-toggle="modal" data-target="#testimonialModal">
                    Submit Your Testimonial
                </button>
            </div>
        </div>
    </div>
</section>
<!-- End Testimonials Area -->
<!-- Testimonial Modal -->
<div class="modal fade" id="testimonialModal" tabindex="-1" role="dialog" aria-labelledby="testimonialModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testimonialModalLabel">Submit Your Testimonial</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form action="{{ route('testimonials.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="form-group">
                        <label for="name">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="country">Country</label>
                        <input type="text" class="form-control" id="country" name="country" required>
                    </div>
                    <div class="form-group">
                        <label for="comment">Comment</label>
                        <textarea class="form-control" id="comment" name="comment" rows="4" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="image">Upload Image</label>
                        <input type="file" class="form-control" id="image" name="image" required>
                    </div>
                    <div class="button">
                    <button type="submit" class="btn">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>



<!-- Start Newsletter Area -->
<section class="newsletter-area section">
    <div class="container">
        <div class="row ">
            <div class="col-lg-6 offset-lg-3 col-md-12 col-12">
                <div class="newsletter-title">
                    <span>Sign Up for</span>
                    <h2>The Newsletter</h2>
                    <p>Subscribe to us to always stay in touch with us and get the latest news<br>
                        about our company and all of our activities!</p>
                </div>
                <!-- Start Newsletter Form -->
                <div class="subscribe-text wow fadeInUp" data-wow-delay=".2s">
                    <form action="{{ route('storeNewsletter') }}" method="post" class="newsletter-inner">
                        @csrf
                        <input name="email" placeholder="Your email address" class="common-input"
                        onfocus="this.placeholder = ''" onblur="this.placeholder = 'Your email address'"
                            required="" type="email">
                        <div class="button">
                            <button class="btn">Subscribe Now!</button>
                        </div>
                    </form>
                    <ul class="newsletter-social">
                        <li><a href="{{ asset('storage/' . setting('site.facebook')) }}"><i class="lni lni-facebook-original"></i></a></li>
                        <li><a href="{{ asset('storage/' . setting('site.twitter')) }}"><i class="lni lni-twitter-original"></i></a></li>
                        <li><a href="{{ asset('storage/' . setting('site.youtube')) }}"><i class="lni lni-youtube"></i></a></li>
                        <li><a href="{{ asset('storage/' . setting('site.linkedin')) }}"><i class="lni lni-linkedin-original"></i></a></li>
                    </ul>
                </div>
                <!-- End Newsletter Form -->
            </div>
        </div>
    </div>
</section>
<!-- /End Newsletter Area -->

<!-- Blog Page Content -->
<div class="latest-news-area section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title">
                    <div class="section-icon wow zoomIn" data-wow-delay=".4s">
                        <i class="lni lni-quotation"></i>
                    </div>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Latest News & Blog</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">There are many variations of passages of Lorem
                        Ipsum available, but the majority have suffered alteration in some form.</p>
                </div>
            </div>
        </div>
        <div class="row">
                    @foreach($posts as $post)
                    <div class="col-lg-4 col-md-6 col-12">
                            <!-- Single News -->
                            <div class="single-news custom-shadow-hover wow fadeInUp" data-wow-delay=".2s">
                                <div class="image">
                                    <a href="{{ route('postShow', $post->slug) }}"><img class="thumb"
                                            src="{{ Voyager::image($post->image) }}" alt="{{ $post->title }}"></a>
                                </div>
                                <div class="content-body">
                                    <div class="meta-data">
                                        <ul>
                                            <li>
                                                <i class="lni lni-tag"></i>
                                                <a href="javascript:void(0)">{{ $post->category->name ?? 'Uncategorized' }}</a>
                                            </li>
                                            <li>
                                                <i class="lni lni-calendar"></i>
                                                <a href="javascript:void(0)">{{ $post->created_at->format('F d, Y') }}</a>
                                            </li>
                                        </ul>
                                    </div>
                                    <h4 class="title"><a href="{{ route('postShow', $post->slug) }}">{{ $post->title }}</a></h4>
                                    <p>{{ Str::limit($post->excerpt, 100) }}</p>
                                    <div class="button">
                                        <a href="{{ route('postShow', $post->slug) }}" class="btn">Read More</a>
                                    </div>
                                </div>
                            </div>
                            <!-- End Single News -->
                        </div>
                    @endforeach
                </div>
                <!--/ End Pagination -->
            </div>
        </div>


<!-- Start Clients Area -->
<div class="client-logo-section">
    <div class="container">
        <div class="client-logo-wrapper">
            <div class="client-logo-carousel d-flex align-items-center justify-content-between">
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="#">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="#">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="#">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="#">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="#">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="#">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="#">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="#">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="#">
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Clients Area -->
@endsection

@section('scripts')
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const observer = new IntersectionObserver(entries => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    startCounterAnimation(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        const counterElements = document.querySelectorAll(".counter");

        counterElements.forEach(element => {
            observer.observe(element);
        });

        function startCounterAnimation(element) {
            const endValue = parseInt(element.querySelector("span").getAttribute("data-end"));
            const duration = 5000;
            const increment = Math.ceil(endValue / (duration / 10));

            let currentValue = 0;
            const intervalId = setInterval(() => {
                currentValue += increment;
                if (currentValue >= endValue) {
                    currentValue = endValue;
                    clearInterval(intervalId);
                }
                element.querySelector("span").textContent = currentValue;
            }, 10);
        }
    });
    </script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <!-- Review Modal JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle Add Review button clicks
        document.querySelectorAll('.add-review-btn').forEach(button => {
            button.addEventListener('click', function() {
                const courseId = this.getAttribute('data-course-id');
                const courseTitle = this.getAttribute('data-course-title');

                // Set course info in modal
                document.getElementById('modal_course_id').value = courseId;
                document.getElementById('modal_course_title').textContent = courseTitle;

                // Reset form
                document.getElementById('reviewForm').reset();
                document.getElementById('modal_rating').value = '5';

                // Reset stars
                const stars = document.querySelectorAll('#modal_star_rating .star');
                stars.forEach((star, index) => {
                    if (index < 5) {
                        star.classList.add('active');
                    } else {
                        star.classList.remove('active');
                    }
                });
            });
        });

        // Handle star rating in modal
        const modalStars = document.querySelectorAll('#modal_star_rating .star');
        const modalRatingInput = document.getElementById('modal_rating');

        modalStars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = this.getAttribute('data-rating');
                modalRatingInput.value = rating;

                // Update visual state
                modalStars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });

            star.addEventListener('mouseover', function() {
                const rating = this.getAttribute('data-rating');
                modalStars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.add('hover');
                    } else {
                        s.classList.remove('hover');
                    }
                });
            });
        });

        document.getElementById('modal_star_rating').addEventListener('mouseleave', function() {
            modalStars.forEach(s => s.classList.remove('hover'));
        });
    });
    </script>
@endsection

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewModalLabel">Add Your Review</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reviewForm" action="{{ route('course-reviews.store') }}" method="POST">
                    @csrf
                    <input type="hidden" name="course_id" id="modal_course_id">

                    <div class="course-info mb-4">
                        <h6 id="modal_course_title"></h6>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="modal_user_name">Your Name *</label>
                                <input type="text" name="user_name" id="modal_user_name" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="modal_user_email">Your Email *</label>
                                <input type="email" name="user_email" id="modal_user_email" class="form-control" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="modal_rating">Your Rating *</label>
                        <div class="rating-input">
                            <input type="hidden" name="rating" id="modal_rating" value="5">
                            <div class="star-rating" id="modal_star_rating">
                                <span class="star" data-rating="1">★</span>
                                <span class="star" data-rating="2">★</span>
                                <span class="star" data-rating="3">★</span>
                                <span class="star" data-rating="4">★</span>
                                <span class="star" data-rating="5">★</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="modal_comment">Your Review *</label>
                        <textarea name="comment" id="modal_comment" class="form-control" rows="4" required placeholder="Share your experience with this course..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="reviewForm" class="btn btn-primary">Submit Review</button>
            </div>
        </div>
    </div>
</div>
