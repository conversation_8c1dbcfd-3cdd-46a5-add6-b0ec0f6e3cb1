<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use TCG\Voyager\Models\DataType;
use TCG\Voyager\Models\DataRow;
use TCG\Voyager\Models\Permission;

class CourseReviewsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create DataType for CourseReview
        $dataType = DataType::firstOrCreate([
            'slug' => 'course-reviews'
        ], [
            'name' => 'course_reviews',
            'display_name_singular' => 'Course Review',
            'display_name_plural' => 'Course Reviews',
            'icon' => 'voyager-star',
            'model_name' => 'App\\Models\\CourseReview',
            'policy_name' => null,
            'controller' => null,
            'generate_permissions' => 1,
            'description' => 'Manage course reviews and ratings',
            'server_side' => 1,
            'details' => json_encode([
                'order_column' => 'created_at',
                'order_display_column' => 'created_at',
                'order_direction' => 'desc',
                'default_search_key' => null,
                'scope' => null
            ])
        ]);

        // Create DataRows for CourseReview
        $dataRows = [
            [
                'field' => 'id',
                'type' => 'number',
                'display_name' => 'ID',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 1,
            ],
            [
                'field' => 'course_id',
                'type' => 'select_dropdown',
                'display_name' => 'Course',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 2,
                'details' => json_encode([
                    'relationship' => [
                        'model' => 'App\\Models\\Course',
                        'table' => 'courses',
                        'type' => 'belongsTo',
                        'column' => 'course_id',
                        'key' => 'id',
                        'label' => 'title',
                        'pivot_table' => 'courses',
                        'pivot' => '0',
                        'taggable' => null
                    ]
                ])
            ],
            [
                'field' => 'user_name',
                'type' => 'text',
                'display_name' => 'User Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 3,
            ],
            [
                'field' => 'user_email',
                'type' => 'text',
                'display_name' => 'User Email',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 4,
            ],
            [
                'field' => 'rating',
                'type' => 'number',
                'display_name' => 'Rating',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 5,
                'details' => json_encode([
                    'min' => 1,
                    'max' => 5,
                    'step' => 1
                ])
            ],
            [
                'field' => 'comment',
                'type' => 'text_area',
                'display_name' => 'Comment',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 6,
            ],
            [
                'field' => 'is_approved',
                'type' => 'checkbox',
                'display_name' => 'Approved',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 7,
                'details' => json_encode([
                    'on' => 'Approved',
                    'off' => 'Pending',
                    'checked' => false
                ])
            ],
            [
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 8,
            ],
            [
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 9,
            ],
        ];

        foreach ($dataRows as $dataRow) {
            DataRow::firstOrCreate([
                'data_type_id' => $dataType->id,
                'field' => $dataRow['field']
            ], $dataRow);
        }

        // Generate permissions
        Permission::generateFor('course-reviews');
    }
}
