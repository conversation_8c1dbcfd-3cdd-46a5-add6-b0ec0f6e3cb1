@extends('layouts.app')

@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ setting('site.title') }}</title>
    <meta name="description" content="{{ setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css">
@endsection

@section('styles')
<style>
    .custom-select {
        position: relative;
        display: inline-block;
        width: 100%;
    }

    .custom-select select {
        width: 100%;
        padding-right: 30px; /* space for flag icon */
    }

    .flag-container {
        position: absolute;
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
    }

    .flag-icon {
        max-width: 35px; /* adjust flag icon size */
        max-height: 48px;
    }


    .iti .iti__country-list {
        display: block;
        max-height: 250px;
        overflow-y: auto;
    }

    .iti .iti__country {
        display: block;
        width: auto;
        margin-bottom: 5px;
    }

    .iti--separate-dial-code input[type=tel] {
        margin-right: 140px;
    }

    .invalid-feedback {
        color: red !important;
        font-size: 14px;
        font-weight: bold;
        margin-top: -8px;
        display: block;
    }
    .is-invalid {
        border: 5px solid red !important;
    }
    </style>
@endsection

@section('content')

    <!-- Start Breadcrumbs -->
    <div class="breadcrumbs overlay">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                    <div class="breadcrumbs-content">
                        <h1 class="page-title">Register</h1>
                        <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
                            been the industry's standard dummy text</p>
                    </div>
                    <ul class="breadcrumb-nav">
                        <li><a href="index.html">Home</a></li>
                        <li>Register</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-- End Breadcrumbs -->

    <!-- start Trial Class section -->
<section class="login section">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 offset-lg-3 col-md-8 offset-md-2 col-12">
                <div class="form-head">
                    <h4 class="title">Trial Class</h4>
                    <form action="{{ route('trial-class.submit') }}" method="post" name="trial-class-form">
                        @csrf
                        <div class="form-group">
                            <label for="firstName">First Name:</label>
                            <input class="margin-5px-bottom form-control @error('first_name') is-invalid @enderror" type="text" id="firstName" name="first_name" placeholder="First Name" value="{{ old('first_name') }}" required>
                            @error('first_name')
                            <div class="alert-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="lastName">Last Name:</label>
                            <input class="margin-5px-bottom form-control @error('last_name') is-invalid @enderror" type="text" id="lastName" name="last_name" placeholder="Last Name" value="{{ old('last_name') }}" required>
                            @error('last_name')
                            <div class="alert-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="email">Email:</label>
                            <input class="margin-5px-bottom form-control @error('email') is-invalid @enderror" type="email" id="email" name="email" placeholder="Email" value="{{ old('email') }}" required>
                            @error('email')
                            <div class="alert-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="phone">Phone:</label>
                            <input class="margin-5px-bottom form-control @error('phone') is-invalid @enderror" type="tel" id="phone" name="phone" placeholder="Phone" value="{{ old('phone') }}" required>
                            @error('phone')
                            <div class="alert-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="country">Country:</label>
                            <div class="custom-select">
                                <select class="form-control @error('country') is-invalid @enderror" id="country-select" name="country" required>
                                    <option value="" disabled selected>Select Country</option>
                                    @foreach($countries as $country)
                                        <option value="{{ $country['name'] }}" data-flag="{{ $country['flag'] }}">{{ $country['name'] }}</option>
                                    @endforeach
                                </select>
                                <div id="flag-container" class="flag-container"></div>
                            </div>
                            @error('country')
                            <div class="alert-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="state">State:</label>
                            <input class="margin-5px-bottom form-control @error('state') is-invalid @enderror" type="text" id="state" name="state" placeholder="State" value="{{ old('state') }}" required>
                            @error('state')
                            <div class="alert-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="chooseCourse">Choose Your Course:</label>
                            <select class="form-control @error('choose_course') is-invalid @enderror" id="chooseCourse" name="choose_course" required>
                                <option value="" disabled selected>Select Course</option>
                                <option {{ old('choose_course') == 'Noor El-Bayaan Course' ? 'selected' : '' }} value="Noor El-Bayaan Course">Noor El-Bayaan Course</option>
                                <option {{ old('choose_course') == 'Quran Courses: Recitation and Memorization with Tajweed (Beginners)' ? 'selected' : '' }} value="Quran Courses: Recitation and Memorization with Tajweed (Beginners)">Quran Courses: Recitation and Memorization with Tajweed (Beginners)</option>
                                <option {{ old('choose_course') == 'Quran Courses: Recitation and Memorization with Tajweed (Advanced)' ? 'selected' : '' }} value="Quran Courses: Recitation and Memorization with Tajweed (Advanced)">Quran Courses: Recitation and Memorization with Tajweed (Advanced)</option>
                                <option {{ old('choose_course') == 'Islamic Studies' ? 'selected' : '' }} value="Islamic Studies">Islamic Studies</option>
                                <option {{ old('choose_course') == 'Ijazah Program' ? 'selected' : '' }} value="Ijazah Program">Ijazah Program</option>
                                <option {{ old('choose_course') == 'Qira\'at Program' ? 'selected' : '' }} value="Qira'at Program">Qira'at Program</option>
                            </select>
                            @error('choose_course')
                            <div class="alert-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="notes">Notes:</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3" placeholder="Notes">{{ old('notes') }}</textarea>
                            @error('notes')
                            <div class="alert-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="button">
                            <button type="submit" class="btn">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Trial Class section -->
@endsection

@section('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const countrySelect = document.getElementById('country-select');
        const flagContainer = document.getElementById('flag-container');

        // Function to update flag icon
        function updateFlag() {
            const selectedOption = countrySelect.options[countrySelect.selectedIndex];
            const flagUrl = selectedOption.getAttribute('data-flag');

            if (flagUrl) {
                flagContainer.innerHTML = `<img src="${flagUrl}" alt="Flag" class="flag-icon">`;
            } else {
                flagContainer.innerHTML = '';
            }
        }

        // Call updateFlag function on page load
        updateFlag();

        // Event listener for select change
        countrySelect.addEventListener('change', updateFlag);
    });
</script>

<script>
    var input = document.querySelector("#phone");
    window.intlTelInput(input, {
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js",
        initialCountry: "auto",
        separateDialCode: true,
        placeholderNumberType: "MOBILE",
        formatOnDisplay: true,
        autoHideDialCode: false,
        autoPlaceholder: "aggressive",
        dropdownContainer: document.body,
        geoIpLookup: function(callback) {
            $.get('https://ipinfo.io', function() {}, "jsonp").always(function(resp) {
                var countryCode = (resp && resp.country) ? resp.country : "";
                callback(countryCode);
            });
        },
        hiddenInput: "phone"
    });

</script>

@endsection
