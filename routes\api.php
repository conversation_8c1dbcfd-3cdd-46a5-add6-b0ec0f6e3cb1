<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\TrialClass;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
Route::get('/unread-trial-classes-count', function () {
    $unreadCount = TrialClass::where('is_read', false)->count();
    return response()->json(['unread_count' => $unreadCount]);
});

