<!doctype html>
<html ⚡ lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
    <title>{{ $post->title }}</title>
    <link rel="canonical" href="{{ url()->current() }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <script async src="https://cdn.ampproject.org/v0.js"></script>
    <style amp-custom>
        /* General Styling */
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #fff;
            color: #333;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px;
            background-color: #315B58;
            color: #fff;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }


        .navbar .menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .navbar .menu a {
            color: #fff;
            text-decoration: none;
            font-size: 16px;
            font-weight: bold;
        }

        .navbar .menu a:hover {
            text-decoration: underline;
        }

        .toggle-btn {
            font-size: 24px;
            color: #fff;
            cursor: pointer;
            display: none;
        }

        .sidebar {
            width: 250px;
            height: 100vh;
            background-color: #222;
            color: #fff;
            position: fixed;
            top: 0;
            left: -250px;
            transition: 0.3s;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar.active {
            left: 0;
        }

        .sidebar .close-btn {
            font-size: 24px;
            position: absolute;
            top: 15px;
            right: 15px;
            cursor: pointer;
        }

        .sidebar ul {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .sidebar ul li {
            padding: 15px 20px;
            border-bottom: 1px solid #444;
        }

        .sidebar ul li a {
            text-decoration: none;
            color: #fff;
            font-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar ul li a:hover {
            background-color: #333;
        }

        .content {
            padding: 20px;
            transition: 0.3s;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
            background: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        header {
            padding: 20px 0;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }

        header img {
            max-width: 150px;
            height: auto;
            margin-bottom: 10px;
        }

        .breadcrumbs {
            font-size: 14px;
            color: #555;
            margin-bottom: 10px;
        }

        .breadcrumbs a {
            color: #315B58;
            text-decoration: none;
        }

        .breadcrumbs a:hover {
            text-decoration: underline;
        }

        .post-title {
            font-size: 26px;
            font-weight: bold;
            margin: 20px 0;
            color: #222;
        }

        .post-image {
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
        }

        .post-image img {
            width: 100%;
            height: auto;
        }

        .post-content {
            font-size: 18px;
            line-height: 1.8;
            text-align: justify;
            color: #444;
            margin-bottom: 30px;
        }

        footer {
            text-align: center;
            font-size: 14px;
            color: #666;
            padding: 20px 0;
            margin-top: 20px;
            border-top: 1px solid #ddd;
        }

        @media (max-width: 768px) {
            .toggle-btn {
                display: block;
            }

            .navbar .menu {
                display: none;
            }

            .sidebar {
                top: 60px;
            }

            .sidebar ul {
                margin-top: 0;
            }
        }
    </style>
</head>
<body>
    <div class="navbar">
        <div class="menu">
            <a href="{{ route('home') }}">Home</a>
            <a href="{{ route('about') }}">About</a>
            <a href="{{ route('contact') }}">Contact</a>
            <a href="{{ route('pricing') }}">Pricing</a>
            <a href="{{ route('courses') }}">Courses</a>
            <a href="{{ route('team') }}">Team</a>
            <a href="{{ route('trial-class') }}">Trial Class</a>
        </div>
        <div class="toggle-btn" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </div>
    </div>

    <div class="sidebar" id="sidebar">
        <span class="close-btn" onclick="toggleSidebar()">&times;</span>
        <ul>
            <li><a href="#">Home</a></li>
            <li><a href="{{ route('about') }}">About</a></li>
            <li><a href="{{ route('courses') }}">Courses</a></li>
            <li><a href="{{ route('contact') }}">Contact</a></li>
            <li><a href="{{ route('pricing') }}">Pricing</a></li>
            <li><a href="{{ route('team') }}">Team</a></li>
            <li><a href="{{ route('trial-class') }}">Trial Class</a></li>
        </ul>
    </div>

    <div class="content" id="content">
        <div class="container">
            <header>
                <a href="{{ route('home') }}">
                    <img src="{{ asset('storage/' . setting('site.logo')) }}" alt="Logo">
                </a>
                <div class="breadcrumbs">
                    <a href="{{ route('home') }}">Home</a> > {{ $post->title }}
                </div>
                <h1 class="post-title">{{ $post->title }}</h1>
            </header>
            <main>
                <div class="post-image">
                    <amp-img src="{{ Voyager::image($post->image) }}" alt="{{ $post->title }}" width="800" height="400" layout="responsive"></amp-img>
                </div>
                <div class="post-content">{!! $post->body !!}</div>
            </main>
            <footer>
                <p>&copy; {{ date('Y') }} Sabeel Ul-Quraan. All rights reserved.</p>
            </footer>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');
            sidebar.classList.toggle('active');
            content.classList.toggle('active');
        }
    </script>
</body>
</html>
