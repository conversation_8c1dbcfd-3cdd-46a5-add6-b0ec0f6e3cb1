@extends('layouts.app')

@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ setting('site.title') }}</title>
    <meta name="description" content="{{ setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">
@endsection

@section('styles')
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Custom Styles -->
    <style>
        .teacher-section {
            background: #f9f9f9;
            padding: 50px 0;
        }
        .teacher-section .section-title {
            text-align: center;
            margin-bottom: 50px;
        }
        .teacher-section .section-title h2 {
            font-size: 2.5em;
            color: #333;
        }
        .teacher-section .section-title p {
            font-size: 1.2em;
            color: #666;
        }
        .teacher-content {
            text-align: center;
            background: #fff;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            margin-bottom: 30px; /* Added margin-bottom for gap */
            height: 100%; /* Fixed height */
            width: 100%; /* Ensures full width within column */
        }
        .teacher-content:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 20px rgba(0, 0, 0, 0.2);
        }
        .teacher-content .teacher-icon {
            font-size: 3em;
            color: #315B58;
            margin-bottom: 20px;
        }
        .teacher-content p {
            font-size: 1.1em;
            line-height: 1.7em;
            color: #555;
            overflow: hidden; /* Ensure text does not overflow */
            text-overflow: ellipsis; /* Ellipsis for overflow text */
            height: 100%; /* Ensure text height consistency */
        }
        .equal-height {
            display: flex;
            flex-wrap: wrap;
            gap: 30px; /* Flex gap for consistent spacing */
        }
        .equal-height > div {
            flex: 1 1 calc(50% - 30px); /* Adjust the flex-basis for even spacing */
        }
        @media (max-width: 768px) {
            .equal-height > div {
                flex: 1 1 calc(100% - 30px); /* Full width for smaller screens */
            }
        }
    </style>
@endsection

@section('content')
<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title"><i class="fas fa-chalkboard-teacher"></i> Celebrating Our Teachers</h1>
                    <p><i class="fas fa-quote-left"></i> At our academy, we deeply value and respect our teachers who dedicate their lives to imparting knowledge and wisdom. Their tireless efforts in helping students memorize the Qur’an form the foundation of our educational success. <i class="fas fa-quote-right"></i></p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="{{ route('home') }}"><i class="fas fa-home"></i> Home</a></li>
                    <li><i class="fas fa-users"></i> Teachers</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start Teacher Section -->
<section id="teachers" class="teacher-section">
    <div class="container">
        <div class="section-title">
            <div class="section-icon wow zoomIn" data-wow-delay=".4s">
                <i class="fas fa-users"></i>
            </div>
            <h2 class="wow fadeInUp" data-wow-delay=".4s">Our Esteemed Educators</h2>
            <p class="wow fadeInUp" data-wow-delay=".6s"><i class="fas fa-quote-left"></i> Our team of dedicated teachers is committed to providing an exceptional educational experience. Their guidance helps students achieve both academic and personal excellence through the memorization of the Qur’an. <i class="fas fa-quote-right"></i></p>
        </div>
        <div class="row equal-height">
            <!-- Teacher Content -->
            <div class="col-lg-6 col-md-6 col-12 wow fadeInUp" data-wow-delay=".2s">
                <div class="teacher-content">
                    <div class="teacher-icon">
                        <i class="fas fa-chalkboard"></i>
                    </div>
                    <p>Our teachers come from diverse backgrounds, each bringing a wealth of knowledge and experience. They are passionate about teaching and are always eager to inspire and support their students. Through their guidance, students not only excel academically but also develop a deep connection with the Qur’an.</p>
                </div>
            </div>
            <div class="col-lg-6 col-md-6 col-12 wow fadeInUp" data-wow-delay=".4s">
                <div class="teacher-content">
                    <div class="teacher-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <p>More than just educators, our teachers are mentors and role models. Their dedication to their profession and their students is evident in everything they do. They continually strive to create a nurturing and stimulating learning environment that fosters growth and success.</p>
                </div>
            </div>
            <div class="col-lg-6 col-md-6 col-12 wow fadeInUp" data-wow-delay=".6s">
                <div class="teacher-content">
                    <div class="teacher-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <p>We are incredibly proud of our teachers and grateful for their contributions. Their hard work and dedication are the pillars of our academy, helping us achieve our mission of providing high-quality education and fostering a love for learning the Qur’an.</p>
                </div>
            </div>
            <div class="col-lg-6 col-md-6 col-12 wow fadeInUp" data-wow-delay=".8s">
                <div class="teacher-content">
                    <div class="teacher-icon">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <p>Join us in celebrating our amazing teachers and their unwavering commitment to excellence in education. Their impact on our students' lives is immeasurable, and we are honored to have them as part of our team.</p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Teacher Section -->
@endsection

@section('scripts')
@endsection
