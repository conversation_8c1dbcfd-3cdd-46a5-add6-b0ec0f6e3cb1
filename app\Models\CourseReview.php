<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CourseReview extends Model
{
    use HasFactory;

    protected $table = 'course_reviews';

    protected $fillable = [
        'course_id',
        'user_name',
        'user_email',
        'rating',
        'comment',
        'is_approved',
        'is_read'
    ];

    protected $casts = [
        'is_approved' => 'boolean',
        'is_read' => 'boolean',
        'rating' => 'integer'
    ];

    // Relationship with Course
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    // Scope for approved reviews only
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    // Scope for pending reviews
    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }

    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    // Get star rating as HTML
    public function getStarRatingAttribute()
    {
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $stars .= '<i class="lni lni-star-filled"></i>';
            } else {
                $stars .= '<i class="lni lni-star"></i>';
            }
        }
        return $stars;
    }
}
