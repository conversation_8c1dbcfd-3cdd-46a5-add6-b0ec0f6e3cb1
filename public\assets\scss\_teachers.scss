/*======================================
	Teachers CSS
========================================*/
.teachers {
    background-color: $gray;

    .section-title {
        margin-bottom: 40px;
    }

    .single-team {
        border: 1px solid #eee;
        background-color: $white;
        margin-top: 30px;
        position: relative;

        &::before {
            position: absolute;
            content: "";
            right: 0;
            top: 0;
            height: 0;
            width: 5px;
            background-color: $theme-color;
            transition: all 0.4s ease-in-out;
        }

        &:hover::before {
            height: 100%;
        }

        .image {
            overflow: hidden;
            padding: 10px 0 10px 10px;

            img {
                width: 100%;
                border: 1px solid #eee;
            }
        }

        .info-head {
            padding: 35px 40px 35px 20px;

            .info-box {
                .designation {
                    font-weight: 600;
                    font-size: 13px;
                    display: block;
                    color: $theme-color;
                    margin-bottom: 3px;
                }

                .name {
                    display: block;
                    margin-bottom: 15px;

                    a {
                        font-weight: 700;
                        font-size: 19px;
                        color: $black;

                        &:hover {
                            color: $theme-color;
                        }
                    }
                }

                p {
                    font-size: 14px;
                    margin-bottom: 20px;
                }
            }

            .social {
                display: block;
                margin-top: 5px;

                li {
                    display: inline-block;
                    margin-right: 5px;
                    margin-top: 10px;

                    &:last-child {
                        margin: 0;
                    }

                    a {
                        height: 40px;
                        width: 40px;
                        line-height: 40px;
                        border: 1px solid #eee;
                        color: $black;
                        border-radius: 50%;
                        font-size: 13px;
                        text-align: center;

                        &:hover {
                            background-color: $theme-color;
                            color: $white;
                            border-color: transparent;
                            transform: rotate(360deg);
                        }
                    }
                }
            }
        }
    }
}

/* Teacher Details */

/*-- teacher Details --*/
.teacher-personal-info {
    background: $gray;
    margin-bottom: 30px;
    padding: 30px;

    .image {
        img {
            border: 1px solid #eee;
            width: 100%;
        }

        .name {
            font-size: 16px;
            font-weight: 600;
            display: block;
            margin-top: 10px;

            span {
                font-size: 12px;
                font-weight: 500;
                display: block;
                color: #777;
                margin-top: 3px;
            }
        }
    }

    .personal-social {
        padding-left: 20px;

        p {
            color: #555;
        }

        .social {
            margin-top: 30px;

            li {
                display: inline-block;
                margin-right: 13px;

                &:last-child {
                    margin-right: 0;
                }

                a {
                    color: $black;
                    font-size: 13px;

                    &:hover {
                        color: $theme-color;
                    }
                }
            }
        }
    }
}

.teacher-details-area .teacher-details-left .social li a:hover {
    color: #fff;
    background: $theme-color;
    border-color: transparent;
}

.teacher-details-item .teacher-details-contact h3.first {
    margin-top: 0;
}

.teacher-details-item .teacher-details-contact h3 {
    font-weight: 600;
    font-size: 20px;
    color: #232323;
    margin-top: 30px;
}


.teacher-details-item .teacher-details-right {
    .right-contact-inner {
        h3 {
            color: $black;
            font-weight: 700;
            font-size: 20px;
        }
    }
}

.teacher-details-item .teacher-details-contact h3 {
    font-weight: 600;
    font-size: 18px;
    color: $black;
    margin-bottom: 20px;
    margin-top: 40px;
}

.teacher-details-item .teacher-details-contact p {
    margin-bottom: 0;
}

.teacher-details-item .teacher-details-contact ul {
    margin: 0;
    padding: 0;
}

.teacher-details-item .teacher-details-contact ul li {
    list-style-type: none;
    display: block;
    margin-bottom: 10px;
}

.teacher-details-item .teacher-details-contact ul li:last-child {
    margin-bottom: 0;
}

.list-description {
    margin-top: 35px;
}

.list-description li {
    color: #868686;
    margin-bottom: 6px;
    position: relative;
    padding-left: 28px;
}

.list-description li:last-child {
    margin-bottom: 0px;
}

.list-description li i {
    text-align: center;
    color: $theme-color;
    font-size: 16px;
    position: absolute;
    left: 0;
    top: 6px;
}

.skill-main h3 {
    margin-top: 0 !important;
}

.skill-main {
    margin-top: 35px;
}

.skill-main .single-skill {
    margin-top: 35px;
}

.skill-main .skill-title h4 {
    color: $black;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 10px;
}

.skill-main .progress {
    box-shadow: none;
    background: #eee;
    height: 4px;
    overflow: visible;
    border-radius: 10px;
}

.skill-main .progress-bar {
    position: relative;
    background: $theme-color;
    border: none;
    text-shadow: none;
    box-shadow: none;
    border-radius: 10px;
    overflow: visible;
}

.skill-main .progress-bar span {
    position: absolute;
    right: 2px;
    top: -40px;
    color: #fff;
    font-weight: 500;
    font-size: 12px;
    background: $theme-color;
    height: 30px;
    width: 35px;
    display: block;
    text-align: center;
    line-height: 30px;
    border-radius: 3px;
}

.skill-main .progress-bar span::before {
    position: absolute;
    content: "";
    right: 0;
    bottom: -6px;
    border: 4px solid $theme-color;
    border-bottom-color: transparent;
    border-left-color: transparent;
}

.teacher-contact-form {
    margin-top: 60px;

    h3 {
        color: $black;
        font-weight: 700;
        font-size: 20px;
        margin-bottom: 25px;
    }

    form {
        .form-group {
            margin-bottom: 20px;

            input {
                width: 100%;
                min-height: 50px;
                padding: 3px 20px;
                color: $black;
                border: 1px solid #f5f5f5;
                border-radius: 0;
                outline: 0;
                background-color: #f5f5f5;
                border: 1px solid #eee;
            }

            textarea {
                width: 100%;
                min-height: 200px;
                padding: 20px;
                color: $black;
                border: 1px solid #f5f5f5;
                border-radius: 0;
                outline: 0;
                background-color: #f5f5f5;
                border: 1px solid #eee;
            }
        }
    }
}