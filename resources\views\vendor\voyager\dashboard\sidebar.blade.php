<div class="side-menu sidebar-inverse">
    <nav class="navbar navbar-default" role="navigation">
        <div class="side-menu-container">
            <div class="navbar-header">
                <a class="navbar-brand" href="{{ route('voyager.dashboard') }}">
                    <div class="logo-icon-container">
                        <?php $admin_logo_img = Voyager::setting('admin.icon_image', ''); ?>
                        @if($admin_logo_img == '')
                            <img src="{{ voyager_asset('images/logo-icon-light.png') }}" alt="Logo Icon">
                        @else
                            <img src="{{ Voyager::image($admin_logo_img) }}" alt="Logo Icon">
                        @endif
                    </div>
                    <div class="title">{{Voyager::setting('admin.title', 'VOYAGER')}}</div>
                </a>
            </div><!-- .navbar-header -->

            <div class="panel widget center bgimage"
                 style="background-image:url({{ Voyager::image( Voyager::setting('admin.bg_image'), voyager_asset('images/bg.jpg') ) }}); background-size: cover; background-position: 0px;">
                <div class="dimmer"></div>
                <div class="panel-content">
                    <img src="{{ $user_avatar }}" class="avatar" alt="{{ Auth::user()->name }} avatar">
                    <h4>{{ ucwords(Auth::user()->name) }}</h4>
                    <p>{{ Auth::user()->email }}</p>

                    <a href="{{ route('voyager.profile') }}" class="btn btn-primary">{{ __('voyager::generic.profile') }}</a>
                    <div style="clear:both"></div>
                </div>
            </div>

        </div>
        <div id="adminmenu">
            <admin-menu :items="{{ menu('admin', '_json') }}"></admin-menu>
        </div>
    </nav>
</div>

<style>
.unread-counter {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 5px;
    display: inline-block;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
    // Function to add unread counters to menu items
    function addUnreadCounters() {
        const unreadCounts = @json($unreadCounts ?? []);
        
        // Add counter for Course Reviews
        if (unreadCounts.course_reviews > 0) {
            addCounterToMenuItem('course-reviews', unreadCounts.course_reviews);
        }
        
        // Add counter for Testimonials  
        if (unreadCounts.testimonials > 0) {
            addCounterToMenuItem('testimonials', unreadCounts.testimonials);
        }
        
        // Add counter for Trial Classes
        if (unreadCounts.trial_classes > 0) {
            addCounterToMenuItem('trial-classes', unreadCounts.trial_classes);
        }
        
        // Add counter for Contacts
        if (unreadCounts.contacts > 0) {
            addCounterToMenuItem('contacts', unreadCounts.contacts);
        }
    }
    
    function addCounterToMenuItem(slug, count) {
        // Wait for Vue.js to render the menu
        setTimeout(() => {
            const menuItem = document.querySelector(`a[href*="${slug}"] .title`);
            if (menuItem && !menuItem.querySelector('.unread-counter')) {
                const counter = document.createElement('span');
                counter.className = 'unread-counter';
                counter.textContent = count;
                menuItem.appendChild(counter);
            }
        }, 1000);
    }
    
    // Initialize counters
    addUnreadCounters();
    
    // Refresh counters every 30 seconds
    setInterval(() => {
        location.reload();
    }, 30000);
});
</script>
