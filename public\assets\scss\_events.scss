/*======================================
    Events CSS
========================================*/
.events {
    .section-title {
        margin-bottom: 40px;
    }

    &.grid-page {
        padding-top: 90px;
    }

    .single-event {
        transition: all 0.4s ease-in-out;
        margin-top: 30px;

        &.short {
            border-top: 1px solid #eee;

            .content {
                position: relative;
                padding-right: 45px;

                .date {
                    position: absolute;
                    right: 10px;
                    top: 0;
                    padding: 5px 11px 5px 10px;
                    background-color: $theme-color;
                    color: #fff;
                    font-size: 14px;
                    font-weight: 800;
                    text-align: center;
                    z-index: 1;
                    padding: 0;
                    padding-top: 8px;
                    padding-bottom: 5px;
                    width: 44px;

                    &::before {
                        position: absolute;
                        content: "";
                        left: 0;
                        bottom: -23px;
                        border: 22px solid $theme-color;
                        border-bottom-color: transparent;
                        z-index: -1;
                    }

                    span {
                        display: block;
                        font-size: 12px;
                        font-weight: 600;
                        text-transform: uppercase;
                        position: relative;
                        top: -2px;
                        margin: 0;
                    }
                }

                h3 {
                    a {
                        font-size: 17px;
                    }
                }
            }
        }

        .event-image {
            position: relative;
            overflow: hidden;

            img {
                width: 100%;
                transition: all 0.4s ease-in-out;
            }

            .date {
                position: absolute;
                right: 20px;
                top: 0;
                padding: 0;
                width: 54px;
                padding-top: 10px;
                background-color: $theme-color;
                color: #fff;
                font-size: 18px;
                font-weight: 800;
                text-align: center;
                z-index: 1;

                &::before {
                    position: absolute;
                    content: "";
                    left: 0;
                    bottom: -38px;
                    border: 27px solid $theme-color;
                    border-bottom-color: transparent;
                    z-index: -1;
                }

                span {
                    display: block;
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                    position: relative;
                    top: -2px;
                    margin: 0;
                }
            }
        }

        &:hover {
            box-shadow: 0px 0px 20px #00000012;

            .event-image {
                img {
                    transform: scale(1.05);
                }
            }
        }

        .content {
            padding: 25px 30px 25px 30px;
            border: 1px solid #eee;
            border-top: none;

            h3 {
                display: block;
                margin-bottom: 15px;

                a {

                    font-size: 20px;
                    font-weight: 700;
                    color: $black;

                    &:hover {
                        color: $theme-color;
                    }
                }
            }
        }

        .bottom-content {
            overflow: hidden;
            border: 1px solid #eee;
            padding: 15px 30px;
            border-top: 30px;

            .speaker {
                float: left;

                img {
                    height: 40px;
                    width: 40px;
                    display: block;
                    margin-right: 8px;
                    border-radius: 50%;
                    display: inline-block;
                }

                span {
                    display: inline-block;
                    color: $black;
                    font-weight: 600;
                }

                &:hover {
                    span {
                        color: $theme-color;
                    }
                }
            }

            .time {
                float: right;
                margin-top: 7px;

                i {
                    color: $theme-color;
                    display: inline-block;
                    margin-right: 3px;
                }

                a {
                    font-size: 13px;
                    color: $black;
                    font-weight: 600;

                    &:hover {
                        color: $theme-color;
                    }
                }
            }
        }
    }
}

/* Event Details */
.event-details {

    /* Event Content */
    .details-content {
        .title {
            font-size: 34px;
            font-weight: 600;
            display: block;
            margin-bottom: 15px;
        }

        .meta-data {
            margin-bottom: 40px;

            li {
                display: inline-block;
                margin-right: 10px;
                font-size: 13px;
                font-weight: 500;
                color: #666;

                &:last-child {
                    margin: 0;
                }

                i {
                    font-size: 15px;
                    display: inline-block;
                    margin-right: 2px;
                    color: $theme-color;
                }
            }
        }

        .text {
            p {
                margin: 25px 0;
                line-height: 24px;
            }

            h4 {
                font-weight: 600;
                font-size: 22px;
                display: block;
                margin: 15px 0;
            }

            .list {
                display: block;
                margin-top: 25px;

                li {
                    display: block;
                    margin-bottom: 15px;
                    position: relative;
                    padding-left: 15px;

                    &::before {
                        position: absolute;
                        content: "";
                        left: 0;
                        top: 8px;
                        height: 7px;
                        width: 7px;
                        display: block;
                        background: $theme-color;
                        border-radius: 50%;
                    }

                    &:last-child {
                        margin: 0;
                    }
                }
            }
        }



        .map-section {
            margin-bottom: 40px;

            .location {
                text-align: center;
                color: $white;
                font-size: 13px;
                background: $theme-color;
                padding: 15px 20px;
                font-weight: 500;

                i {
                    color: $white;
                    font-size: 15px;
                    display: inline-block;
                    margin-right: 4px;
                }
            }
        }
    }

    /* Event Sidebar */
    .event-sidebar {

        .single-widget {
            background: #fff;
            border: 1px solid #eee;
            border-radius: 0;
            padding: 40px;
            margin-bottom: 40px;

            &.first-wedget {
                padding-top: 15px;
            }

            &.other-event-wedget {
                padding-bottom: 15px;
            }

            &:last-child {
                margin: 0;
            }

            .sidebar-widget-title {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 30px;
            }

            .single-event {
                position: relative;
                margin-bottom: 30px;
                padding-bottom: 30px;
                border-bottom: 1px solid #eee;
                overflow: hidden;
                min-height: 130px;

                &:last-child {
                    margin: 0;
                    padding: 0;
                    border: none;
                }

                .thumbnail {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100px;
                    height: 100px;
                    border-radius: 50%;
                    overflow: hidden;

                    &:hover {
                        img {
                            transform: scale(1.1);
                        }
                    }
                }

                .thumbnail img {
                    width: 100px;
                    height: 100px;
                    border-radius: 50%;
                    -webkit-transition: all 0.4s ease;
                    transition: all 0.4s ease;
                }

                .info {
                    display: inline-block;
                    padding: 0;
                    padding-left: 0px;
                    padding-left: 120px;

                    .date {
                        i {
                            display: inline-block;
                            color: $theme-color;
                            margin-right: 4px;
                        }

                        font-size: 13px;
                        font-weight: 500;
                    }

                    .title {
                        line-height: 20px;
                        margin-top: 8px;

                        a {
                            font-size: 15px;
                            font-weight: 500;
                            color: $black;

                            &:hover {
                                color: $theme-color;
                            }
                        }
                    }
                }
            }
        }

        .sidebar-entry-event {
            border-radius: 0;


            .entry-event-info {
                list-style-type: none;
                margin: 0 0 20px;
                padding: 0;

                li {
                    margin: 0;
                    padding: 14px 0;
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-align: center;
                    -ms-flex-align: center;
                    align-items: center;
                    -webkit-box-flex: 1;
                    -ms-flex-positive: 1;
                    flex-grow: 1;

                    .meta-label {
                        -webkit-box-flex: 1;
                        -ms-flex-positive: 1;
                        flex-grow: 1;
                        font-size: 15px;
                        font-weight: 600;
                        line-height: 1.3;
                        color: #333;

                        .meta-icon {
                            color: #696969;
                            min-width: 28px;
                            text-align: center;
                        }
                    }

                    .meta-value {
                        -webkit-box-flex: 1;
                        -ms-flex-positive: 1;
                        flex-grow: 1;
                        text-align: right;

                        .event-price {
                            font-size: 24px;
                            font-weight: 700;
                            color: $theme-color;
                        }
                    }
                }

                li+li {
                    border-top: 1px solid #eee;
                }
            }

            .button {
                margin: 15px 0;
                width: 100%;
                display: block;

                .btn {
                    width: 100% !important;
                    display: block !important;
                }
            }

            .event-register-message {
                margin-top: 10px;
                text-align: center;
                color: $black;
                font-size: 13px;
                font-weight: 500;

                a {
                    color: $theme-color;

                    &:hover {
                        color: $black;
                        text-decoration: underline;
                    }
                }
            }

            .author-social-networks {
                text-align: center;
                margin-top: 30px;

                li {
                    display: inline-block;
                    margin-right: 2px;


                    &:last-child {
                        margin: 0;
                    }

                    a {
                        height: 40px;
                        width: 40px;
                        line-height: 40px;
                        text-align: center;
                        display: block;
                        border: 1px solid #eee;
                        color: #777;
                        border-radius: 0;
                        font-size: 13px;

                        &:hover {
                            border-color: transparent;
                            color: $white;
                            background: $theme-color;
                        }
                    }
                }
            }
        }
    }
}