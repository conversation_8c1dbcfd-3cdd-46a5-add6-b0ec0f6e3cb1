<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use TCG\Voyager\Http\Controllers\VoyagerBaseController;
use App\Models\CourseReview;

class CourseReviewController extends VoyagerBaseController
{
    public function show(Request $request, $id)
    {
        // Mark as read when viewing
        $courseReview = CourseReview::findOrFail($id);
        if (!$courseReview->is_read) {
            $courseReview->update(['is_read' => true]);
        }

        return parent::show($request, $id);
    }

    public function edit(Request $request, $id)
    {
        // Mark as read when editing
        $courseReview = CourseReview::findOrFail($id);
        if (!$courseReview->is_read) {
            $courseReview->update(['is_read' => true]);
        }

        return parent::edit($request, $id);
    }
}
