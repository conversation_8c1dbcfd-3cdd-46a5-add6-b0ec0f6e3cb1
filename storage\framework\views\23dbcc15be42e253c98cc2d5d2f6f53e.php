<div class="side-menu sidebar-inverse">
    <nav class="navbar navbar-default" role="navigation">
        <div class="side-menu-container">
            <div class="navbar-header">
                <a class="navbar-brand" href="<?php echo e(route('voyager.dashboard')); ?>">
                    <div class="logo-icon-container">
                        <?php $admin_logo_img = Voyager::setting('admin.icon_image', ''); ?>
                        <?php if($admin_logo_img == ''): ?>
                            <img src="<?php echo e(voyager_asset('images/logo-icon-light.png')); ?>" alt="Logo Icon">
                        <?php else: ?>
                            <img src="<?php echo e(Voyager::image($admin_logo_img)); ?>" alt="Logo Icon">
                        <?php endif; ?>
                    </div>
                    <div class="title"><?php echo e(Voyager::setting('admin.title', 'VOYAGER')); ?></div>
                </a>
            </div><!-- .navbar-header -->

            <div class="panel widget center bgimage"
                 style="background-image:url(<?php echo e(Voyager::image( Voyager::setting('admin.bg_image'), voyager_asset('images/bg.jpg') )); ?>); background-size: cover; background-position: 0px;">
                <div class="dimmer"></div>
                <div class="panel-content">
                    <img src="<?php echo e($user_avatar); ?>" class="avatar" alt="<?php echo e(Auth::user()->name); ?> avatar">
                    <h4><?php echo e(ucwords(Auth::user()->name)); ?></h4>
                    <p><?php echo e(Auth::user()->email); ?></p>

                    <a href="<?php echo e(route('voyager.profile')); ?>" class="btn btn-primary"><?php echo e(__('voyager::generic.profile')); ?></a>
                    <div style="clear:both"></div>
                </div>
            </div>

        </div>
        <div id="adminmenu">
            <admin-menu :items="<?php echo e(menu('admin', '_json')); ?>"></admin-menu>
        </div>
    </nav>
</div>

<style>
.unread-counter {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 5px;
    display: inline-block;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
    // Function to add unread counters to menu items
    function addUnreadCounters() {
        const unreadCounts = <?php echo json_encode($unreadCounts ?? [], 15, 512) ?>;

        // Add counter for Course Reviews
        if (unreadCounts.course_reviews > 0) {
            addCounterToMenuItem('course-reviews', unreadCounts.course_reviews);
        }

        // Add counter for Testimonials
        if (unreadCounts.testimonials > 0) {
            addCounterToMenuItem('testimonial', unreadCounts.testimonials);
        }

        // Add counter for Trial Classes
        if (unreadCounts.trial_classes > 0) {
            addCounterToMenuItem('trial-class', unreadCounts.trial_classes);
        }

        // Add counter for Contacts
        if (unreadCounts.contacts > 0) {
            addCounterToMenuItem('contact', unreadCounts.contacts);
        }
    }

    function addCounterToMenuItem(slug, count) {
        // Multiple attempts with different selectors
        const attempts = [
            () => document.querySelector(`a[href*="${slug}"] .title`),
            () => document.querySelector(`a[href*="${slug}"]`),
            () => {
                const links = document.querySelectorAll('a[href*="admin/"]');
                for (let link of links) {
                    if (link.href.includes(slug)) {
                        return link.querySelector('.title') || link;
                    }
                }
                return null;
            }
        ];

        function tryAddCounter(attemptIndex = 0) {
            if (attemptIndex >= attempts.length) return;

            const menuItem = attempts[attemptIndex]();

            if (menuItem && !menuItem.querySelector('.unread-counter')) {
                const counter = document.createElement('span');
                counter.className = 'unread-counter';
                counter.textContent = count;

                if (menuItem.classList.contains('title')) {
                    menuItem.appendChild(counter);
                } else {
                    const titleElement = menuItem.querySelector('.title');
                    if (titleElement) {
                        titleElement.appendChild(counter);
                    } else {
                        menuItem.appendChild(counter);
                    }
                }
            } else {
                // Try next attempt
                setTimeout(() => tryAddCounter(attemptIndex + 1), 500);
            }
        }

        // Wait for Vue.js to render the menu
        setTimeout(() => tryAddCounter(), 1000);
        setTimeout(() => tryAddCounter(), 2000);
        setTimeout(() => tryAddCounter(), 3000);
    }

    // Initialize counters
    addUnreadCounters();
});
</script>
<?php /**PATH D:\laragon\www\Sabeel-Ul-Quraan\resources\views/vendor/voyager/dashboard/sidebar.blade.php ENDPATH**/ ?>