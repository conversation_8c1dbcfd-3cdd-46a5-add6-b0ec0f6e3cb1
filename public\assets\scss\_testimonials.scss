/*======================================
    Testimonial CSS
========================================*/
.testimonials {
    background-color: $theme-color;
    padding-bottom: 180px;

    &.style2 {
        background-color: $white;

        .tns-nav {

            button {
                background-color: $theme-color;
            }
        }

        .section-title {
            i {
                color: $theme-color;
                background-color: #00a6511c;
            }

            h2 {
                color: $black;

                &::before {
                    background-color: $theme-color;
                }
            }

            p {
                color: #888;
            }
        }


        .single-testimonial {
            transition: all 0.4s ease-in-out;

            .text {
                border: 2px solid #eee;
                transition: all 0.4s ease-in-out;

                &::before {
                    position: absolute;
                    content: "";
                    left: 50%;
                    bottom: -20px;
                    border: 10px solid #eee;
                    border-left-color: transparent;
                    border-right-color: transparent;
                    border-bottom-color: transparent;
                    margin-left: -10px;
                    transition: all 0.4s ease-in-out;
                }
            }

            &:hover {
                .text {
                    border-color: $theme-color !important;

                    &::before {
                        position: absolute;
                        content: "";
                        left: 50%;
                        bottom: -20px;
                        border: 10px solid $theme-color;
                        border-left-color: transparent;
                        border-right-color: transparent;
                        border-bottom-color: transparent;
                        margin-left: -10px;
                    }
                }

            }

        }

        .author {
            img {
                height: 80px;
                width: 80px;
                display: block;
                border-radius: 50%;
                display: inline-block;
                transition: all 0.4s ease;
            }

            .name {
                color: $black !important;

                span {
                    color: #777;
                }
            }
        }
    }

    .section-title {
        i {
            color: $white;
            background-color: #fff3;
        }

        h2 {
            color: $white;

            &::before {
                background-color: $white;
            }
        }

        p {
            color: $white;
        }
    }

    .testimonial-slider {
        margin: 0;
    }

    .single-testimonial {
        text-align: center;

        .text {
            padding: 40px;
            background-color: $white;
            margin-bottom: 30px;
            position: relative;

            &::before {
                position: absolute;
                content: "";
                left: 50%;
                bottom: -20px;
                border: 10px solid #fff;
                border-left-color: transparent;
                border-right-color: transparent;
                border-bottom-color: transparent;
                margin-left: -10px;
            }
        }

        .author {
            img {
                height: 80px;
                width: 80px;
                display: block;
                border-radius: 50%;
                display: inline-block;
                transition: all 0.4s ease;
            }

            .name {
                font-size: 17px;
                font-weight: 600;
                margin-top: 20px;
                color: $white;

                span {
                    font-size: 13px;
                    display: block;
                    font-weight: 400;
                    margin-top: 4px;
                }
            }
        }

        &:hover {
            .author {}
        }
    }

    .tns-nav {
        text-align: center;
        position: absolute;
        bottom: 90px;
        transform: translateX(-50%);
        width: 100%;
        left: 50%;
        z-index: 9;

        button {
            height: 10px;
            width: 10px;
            background-color: $white;
            border-radius: 30px;
            display: inline-block;
            border: none;
            margin: 0px 5px;
            transition: all 0.4s ease;

            &.tns-nav-active {
                width: 20px;
            }
        }
    }
}