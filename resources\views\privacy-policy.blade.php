{{-- policy_view --}}
@extends('layouts.app')

@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ setting('site.title') }} - Privacy Policy</title>
    <meta name="description" content="{{ setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">
@endsection

@section('styles')
<style>
    .policy-section {
        padding: 50px 0;
        background: #f8f9fa;
    }
    .policy-header {
        text-align: center;
        margin-bottom: 30px;
        animation: fadeInDown 1s ease-in-out;
    }
    .policy-content {
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 30px;
        animation: fadeInUp 1s ease-in-out;
    }
    .policy-content h2 {
        margin-top: 30px;
        font-size: 1.5rem;
    }
    .policy-content p {
        line-height: 1.8;
        font-size: 1rem;
        color: #555;
    }
    .card {
        background: #fff;
        border: 1px solid #e3e3e3;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e3e3e3;
        padding: 10px 20px;
        font-size: 1.25rem;
        font-weight: 600;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }
    .card-body {
        padding: 20px;
    }
    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
@endsection

@section('content')
<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title">Privacy Policy</h1>
                    <p>Your privacy is critically important to us.</p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="{{ url('/') }}">Home</a></li>
                    <li>Privacy Policy</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start Policy Section -->
<section class="policy-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-md-12 col-12">
                <div class="policy-header">
                    <h2>Privacy Policy</h2>
                    <p>Last updated: {{ \Carbon\Carbon::now()->format('F d, Y') }}</p>
                </div>
                <div class="policy-content">
                    <div class="card">
                        <div class="card-header">1. Introduction</div>
                        <div class="card-body">
                            <p>Welcome to Sabeel Ul-Qraan. We are committed to protecting your personal information and your right to privacy. If you have any questions or concerns about our policy, or our practices with regards to your personal information, please contact us at {{ setting('site.contact_email') }}.</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">2. Information We Collect</div>
                        <div class="card-body">
                            <p>We collect personal information that you voluntarily provide to us when registering at the Services, expressing an interest in obtaining information about us or our products and services, when participating in activities on the Services or otherwise contacting us.</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">3. How We Use Your Information</div>
                        <div class="card-body">
                            <p>We use personal information collected via our Services for a variety of business purposes described below. We process your personal information for these purposes in reliance on our legitimate business interests, in order to enter into or perform a contract with you, with your consent, and/or for compliance with our legal obligations.</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">4. Sharing Your Information</div>
                        <div class="card-body">
                            <p>We may process or share data based on the following legal basis: Consent, Legitimate Interests, Performance of a Contract, Legal Obligations, Vital Interests.</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">5. Security of Your Information</div>
                        <div class="card-body">
                            <p>We use administrative, technical, and physical security measures to help protect your personal information. While we have taken reasonable steps to secure the personal information you provide to us, please be aware that despite our efforts, no security measures are perfect or impenetrable, and no method of data transmission can be guaranteed against any interception or other type of misuse.</p>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">6. Contact Us</div>
                        <div class="card-body">
                            <p>If you have questions or comments about this policy, you may contact us by email at {{ setting('site.email') }}, or by post to: {{ setting('site.address') }}.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Policy Section -->
@endsection

@section('scripts')
@endsection
