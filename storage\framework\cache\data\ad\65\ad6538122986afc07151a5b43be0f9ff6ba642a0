1754237598O:23:"TCG\Voyager\Models\Menu":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"menus";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:1;s:4:"name";s:5:"admin";s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-13 21:34:11";}s:11:" * original";a:4:{s:2:"id";i:1;s:4:"name";s:5:"admin";s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-13 21:34:11";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"parent_items";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:18:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:1;s:7:"menu_id";i:1;s:5:"title";s:9:"Dashboard";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-boat";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:1;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-13 21:34:11";s:5:"route";s:17:"voyager.dashboard";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:1;s:7:"menu_id";i:1;s:5:"title";s:9:"Dashboard";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-boat";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:1;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-13 21:34:11";s:5:"route";s:17:"voyager.dashboard";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:2;s:7:"menu_id";i:1;s:5:"title";s:5:"Media";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:4;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:28";s:5:"route";s:19:"voyager.media.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:2;s:7:"menu_id";i:1;s:5:"title";s:5:"Media";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:4;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:28";s:5:"route";s:19:"voyager.media.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:2;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:3;s:7:"menu_id";i:1;s:5:"title";s:5:"Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-person";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:3;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-13 21:34:11";s:5:"route";s:19:"voyager.users.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:3;s:7:"menu_id";i:1;s:5:"title";s:5:"Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-person";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:3;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-13 21:34:11";s:5:"route";s:19:"voyager.users.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:3;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:4;s:7:"menu_id";i:1;s:5:"title";s:5:"Roles";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-lock";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:2;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-13 21:34:11";s:5:"route";s:19:"voyager.roles.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:4;s:7:"menu_id";i:1;s:5:"title";s:5:"Roles";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-lock";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:2;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-13 21:34:11";s:5:"route";s:19:"voyager.roles.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:4;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:5;s:7:"menu_id";i:1;s:5:"title";s:5:"Tools";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-tools";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:8;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";N;s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:5;s:7:"menu_id";i:1;s:5:"title";s:5:"Tools";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-tools";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:8;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";N;s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:6;s:7:"menu_id";i:1;s:5:"title";s:12:"Menu Builder";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-list";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:1;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:19:"voyager.menus.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:6;s:7:"menu_id";i:1;s:5:"title";s:12:"Menu Builder";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-list";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:1;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:19:"voyager.menus.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:7;s:7:"menu_id";i:1;s:5:"title";s:8:"Database";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-data";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:2;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:22:"voyager.database.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:7;s:7:"menu_id";i:1;s:5:"title";s:8:"Database";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-data";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:2;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:22:"voyager.database.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:2;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:8;s:7:"menu_id";i:1;s:5:"title";s:7:"Compass";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:15:"voyager-compass";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:3;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:21:"voyager.compass.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:8;s:7:"menu_id";i:1;s:5:"title";s:7:"Compass";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:15:"voyager-compass";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:3;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:21:"voyager.compass.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:3;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:9;s:7:"menu_id";i:1;s:5:"title";s:5:"BREAD";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-bread";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:4;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:19:"voyager.bread.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:9;s:7:"menu_id";i:1;s:5:"title";s:5:"BREAD";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-bread";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:4;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:19:"voyager.bread.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:5;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:10;s:7:"menu_id";i:1;s:5:"title";s:8:"Settings";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-settings";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:9;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:22:"voyager.settings.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:10;s:7:"menu_id";i:1;s:5:"title";s:8:"Settings";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-settings";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:9;s:10:"created_at";s:19:"2024-05-13 21:34:11";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:22:"voyager.settings.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:6;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:11;s:7:"menu_id";i:1;s:5:"title";s:10:"Categories";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:18:"voyager-categories";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:7;s:10:"created_at";s:19:"2024-05-13 21:34:16";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:24:"voyager.categories.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:11;s:7:"menu_id";i:1;s:5:"title";s:10:"Categories";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:18:"voyager-categories";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:7;s:10:"created_at";s:19:"2024-05-13 21:34:16";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:24:"voyager.categories.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:7;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:12;s:7:"menu_id";i:1;s:5:"title";s:5:"Posts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-news";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:5;s:10:"created_at";s:19:"2024-05-13 21:34:18";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:19:"voyager.posts.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:12;s:7:"menu_id";i:1;s:5:"title";s:5:"Posts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-news";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:5;s:10:"created_at";s:19:"2024-05-13 21:34:18";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:19:"voyager.posts.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:8;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:13;s:7:"menu_id";i:1;s:5:"title";s:5:"Pages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:17:"voyager-file-text";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:6;s:10:"created_at";s:19:"2024-05-13 21:34:18";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:19:"voyager.pages.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:13;s:7:"menu_id";i:1;s:5:"title";s:5:"Pages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:17:"voyager-file-text";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:6;s:10:"created_at";s:19:"2024-05-13 21:34:18";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:19:"voyager.pages.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:9;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:14;s:7:"menu_id";i:1;s:5:"title";s:12:"Testimonials";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-people";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:10;s:10:"created_at";s:19:"2024-05-14 17:48:35";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:25:"voyager.testimonial.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:14;s:7:"menu_id";i:1;s:5:"title";s:12:"Testimonials";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-people";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:10;s:10:"created_at";s:19:"2024-05-14 17:48:35";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:25:"voyager.testimonial.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:10;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:15;s:7:"menu_id";i:1;s:5:"title";s:8:"Contacts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-chat";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:11;s:10:"created_at";s:19:"2024-05-14 17:55:36";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:21:"voyager.contact.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:15;s:7:"menu_id";i:1;s:5:"title";s:8:"Contacts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-chat";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:11;s:10:"created_at";s:19:"2024-05-14 17:55:36";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:21:"voyager.contact.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:11;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:16;s:7:"menu_id";i:1;s:5:"title";s:5:"Teams";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-people";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:12;s:10:"created_at";s:19:"2024-05-15 12:21:37";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:18:"voyager.team.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:16;s:7:"menu_id";i:1;s:5:"title";s:5:"Teams";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-people";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:12;s:10:"created_at";s:19:"2024-05-15 12:21:37";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:18:"voyager.team.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:12;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:17;s:7:"menu_id";i:1;s:5:"title";s:13:"Trial Classes";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-group";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:13;s:10:"created_at";s:19:"2024-05-15 19:37:39";s:10:"updated_at";s:19:"2024-05-24 23:55:46";s:5:"route";s:25:"voyager.trial-class.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:17;s:7:"menu_id";i:1;s:5:"title";s:13:"Trial Classes";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-group";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:13;s:10:"created_at";s:19:"2024-05-15 19:37:39";s:10:"updated_at";s:19:"2024-05-24 23:55:46";s:5:"route";s:25:"voyager.trial-class.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:13;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:18;s:7:"menu_id";i:1;s:5:"title";s:11:"Newsletters";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-certificate";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:14;s:10:"created_at";s:19:"2024-05-16 00:38:09";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:24:"voyager.newsletter.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:18;s:7:"menu_id";i:1;s:5:"title";s:11:"Newsletters";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-certificate";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:14;s:10:"created_at";s:19:"2024-05-16 00:38:09";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:24:"voyager.newsletter.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:14;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:19;s:7:"menu_id";i:1;s:5:"title";s:18:"Courses Categories";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:15:"voyager-logbook";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:15;s:10:"created_at";s:19:"2024-05-16 23:21:24";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:32:"voyager.courses-categories.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:19;s:7:"menu_id";i:1;s:5:"title";s:18:"Courses Categories";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:15:"voyager-logbook";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:15;s:10:"created_at";s:19:"2024-05-16 23:21:24";s:10:"updated_at";s:19:"2024-05-21 23:30:29";s:5:"route";s:32:"voyager.courses-categories.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:15;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:20;s:7:"menu_id";i:1;s:5:"title";s:7:"Courses";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:21:"voyager-documentation";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:16;s:10:"created_at";s:19:"2024-05-16 23:44:39";s:10:"updated_at";s:19:"2024-05-21 23:30:30";s:5:"route";s:21:"voyager.courses.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:20;s:7:"menu_id";i:1;s:5:"title";s:7:"Courses";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:21:"voyager-documentation";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:16;s:10:"created_at";s:19:"2024-05-16 23:44:39";s:10:"updated_at";s:19:"2024-05-21 23:30:30";s:5:"route";s:21:"voyager.courses.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:16;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:24;s:7:"menu_id";i:1;s:5:"title";s:8:"Comments";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-people";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:17;s:10:"created_at";s:19:"2024-05-18 17:30:54";s:10:"updated_at";s:19:"2024-05-21 23:30:30";s:5:"route";s:21:"voyager.comment.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:24;s:7:"menu_id";i:1;s:5:"title";s:8:"Comments";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-people";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:17;s:10:"created_at";s:19:"2024-05-18 17:30:54";s:10:"updated_at";s:19:"2024-05-21 23:30:30";s:5:"route";s:21:"voyager.comment.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:17;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:25;s:7:"menu_id";i:1;s:5:"title";s:8:"Pricings";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-certificate";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:18;s:10:"created_at";s:19:"2024-06-08 17:47:39";s:10:"updated_at";s:19:"2024-06-08 17:47:39";s:5:"route";s:21:"voyager.pricing.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:25;s:7:"menu_id";i:1;s:5:"title";s:8:"Pricings";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-certificate";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:18;s:10:"created_at";s:19:"2024-06-08 17:47:39";s:10:"updated_at";s:19:"2024-06-08 17:47:39";s:5:"route";s:21:"voyager.pricing.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}