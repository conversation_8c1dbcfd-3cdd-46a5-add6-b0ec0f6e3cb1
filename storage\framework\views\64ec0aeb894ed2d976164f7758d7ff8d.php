<?php $__env->startSection('seo'); ?>
    <!-- Meta Tags for SEO -->
    <title><?php echo e(setting('site.title')); ?></title>
    <meta name="description" content="<?php echo e(setting('site.description')); ?>">
    <meta name="keywords" content="<?php echo e(setting('site.keywords')); ?>">

    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>

<style>
    .course-description {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
    }

    /* Course Reviews Styles */
    .course-reviews {
        border-top: 1px solid #eee;
        padding-top: 30px;
    }

    .course-reviews .title {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
    }

    .reviews-summary {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
    }

    .average-rating {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .rating-number {
        font-size: 2rem;
        font-weight: bold;
        color: #F6B500;
    }

    .stars i {
        color: #F6B500;
        font-size: 18px;
    }

    .total-reviews {
        color: #666;
        font-size: 14px;
    }

    .single-review {
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }

    .reviewer-name {
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .review-rating i {
        color: #F6B500;
        font-size: 14px;
    }

    .review-date {
        color: #999;
        font-size: 13px;
    }

    .review-content p {
        margin-bottom: 0;
        color: #666;
        line-height: 1.6;
    }

    .no-reviews {
        text-align: center;
        color: #999;
        font-style: italic;
        padding: 40px 0;
    }

    .add-review-form {
        background: #f8f9fa;
        padding: 30px;
        border-radius: 8px;
    }

    .add-review-form h4 {
        margin-bottom: 20px;
        color: #333;
    }

    .star-rating {
        display: flex;
        gap: 5px;
        margin-top: 5px;
    }

    .star-rating .star {
        font-size: 24px;
        color: #ddd;
        cursor: pointer;
        transition: color 0.2s;
    }

    .star-rating .star:hover,
    .star-rating .star.hover {
        color: #F6B500;
    }

    .star-rating .star.active {
        color: #F6B500;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
        color: #333;
    }

    .form-control {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px 15px;
        font-size: 14px;
    }

    .form-control:focus {
        border-color: #F6B500;
        box-shadow: 0 0 0 0.2rem rgba(246, 181, 0, 0.25);
    }

    .text-danger {
        font-size: 12px;
        margin-top: 5px;
        display: block;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="breadcrumbs overlay" style="background-image: url('<?php echo e(asset('storage/' . $course->image)); ?>') !important;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title"><?php echo e($course->title); ?></h1>
                    <p><?php echo e($course->excerpt); ?></p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="<?php echo e(url('/')); ?>">Home</a></li>
                    <li><?php echo e($course->title); ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<section class="course-details section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-7 col-12">
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                        <div class="course-overview">
                            <h3 class="title">About This Course</h3>
                            <p><?php echo e($course->description); ?></p>
                            <?php if(!empty($course->video_url)): ?>
                            <?php
                                // Extract the video ID from the URL
                                $url_parts = parse_url($course->video_url);
                                parse_str($url_parts['query'], $query_params);
                                $video_id = $query_params['v'] ?? null;
                                $embed_url = $video_id ? 'https://www.youtube.com/embed/' . $video_id : null;
                            ?>

                            <?php if($embed_url): ?>
                                <div class="overview-course-video">
                                    <iframe src="<?php echo e($embed_url); ?>" frameborder="0" allowfullscreen></iframe>
                                </div>
                            <?php else: ?>
                                <div class="overview-course-picture">
                                    <img src="<?php echo e(asset('storage/' . $course->image)); ?>" alt="Course Picture" style="width: 100%; height: auto;">
                                </div>
                            <?php endif; ?>
                            <?php else: ?>
                                <div class="overview-course-picture">
                                    <img src="<?php echo e(asset('storage/' . $course->image)); ?>" alt="Course Picture" style="width: 100%; height: auto;">
                                </div>
                            <?php endif; ?>
                            <p><?php echo e($course->additional_info); ?></p>
                            <div class="bottom-content">
                                <div class="row align-items-center">
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="button">
                                            
                                            <a href="<?php echo e(route('trial-class')); ?>" class="btn">Buy this course</a>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <ul class="share">
                                            <li><span>Share this course:</span></li>
                                            <li><a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(url()->current()); ?>" target="_blank"><i class="lni lni-facebook-original"></i></a></li>
                                            <li><a href="https://twitter.com/intent/tweet?url=<?php echo e(url()->current()); ?>" target="_blank"><i class="lni lni-twitter-original"></i></a></li>
                                            <li><a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e(url()->current()); ?>" target="_blank"><i class="lni lni-linkedin-original"></i></a></li>
                                            <li><a href="mailto:?subject=Check out this course&body=<?php echo e(url()->current()); ?>" target="_blank"><i class="lni lni-google"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Add other tabs here if necessary -->
                </div>

                <!-- Course Reviews Section -->
                <div class="course-reviews mt-5">
                    <h3 class="title">Course Reviews</h3>

                    <?php if($reviews->count() > 0): ?>
                        <div class="reviews-summary mb-4">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="rating-overview">
                                        <div class="average-rating">
                                            <span class="rating-number"><?php echo e($course->average_rating); ?></span>
                                            <div class="stars">
                                                <?php echo $course->star_rating; ?>

                                            </div>
                                            <span class="total-reviews">(<?php echo e($course->reviews_count); ?> <?php echo e($course->reviews_count == 1 ? 'Review' : 'Reviews'); ?>)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="reviews-list">
                            <?php $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="single-review mb-4">
                                    <div class="review-header d-flex justify-content-between align-items-start">
                                        <div class="reviewer-info">
                                            <h6 class="reviewer-name"><?php echo e($review->user_name); ?></h6>
                                            <div class="review-rating">
                                                <?php echo $review->star_rating; ?>

                                            </div>
                                        </div>
                                        <span class="review-date"><?php echo e($review->created_at->format('M d, Y')); ?></span>
                                    </div>
                                    <div class="review-content mt-2">
                                        <p><?php echo e($review->comment); ?></p>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <p class="no-reviews">No reviews yet. Be the first to review this course!</p>
                    <?php endif; ?>

                    <!-- Add Review Form -->
                    <div class="add-review-form mt-5">
                        <h4>Add Your Review</h4>
                        <form action="<?php echo e(route('course-reviews.store')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="course_id" value="<?php echo e($course->id); ?>">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="user_name">Your Name *</label>
                                        <input type="text" name="user_name" id="user_name" class="form-control" required value="<?php echo e(old('user_name')); ?>">
                                        <?php $__errorArgs = ['user_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="user_email">Your Email *</label>
                                        <input type="email" name="user_email" id="user_email" class="form-control" required value="<?php echo e(old('user_email')); ?>">
                                        <?php $__errorArgs = ['user_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="rating">Your Rating *</label>
                                <div class="rating-input">
                                    <input type="hidden" name="rating" id="rating" value="<?php echo e(old('rating', 5)); ?>">
                                    <div class="star-rating">
                                        <span class="star" data-rating="1">★</span>
                                        <span class="star" data-rating="2">★</span>
                                        <span class="star" data-rating="3">★</span>
                                        <span class="star" data-rating="4">★</span>
                                        <span class="star" data-rating="5">★</span>
                                    </div>
                                </div>
                                <?php $__errorArgs = ['rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="comment">Your Review *</label>
                                <textarea name="comment" id="comment" class="form-control" rows="5" required placeholder="Share your experience with this course..."><?php echo e(old('comment')); ?></textarea>
                                <?php $__errorArgs = ['comment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <button type="submit" class="btn btn-primary">Submit Review</button>
                        </form>
                    </div>
                </div>
            </div>
            <!-- Start Course Sidebar -->
            <aside class="col-lg-4 col-md-5 col-12">
                <div class="course-sidebar">
                    <div class="sidebar mb-4">
                        <div class="widget categories-widget">
                            <h5 class="widget-title">Categories</h5>
                            <ul class="custom">
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>
                                        <a href="<?php echo e(route('category.filter.course', $category->slug)); ?>"><?php echo e($category->title); ?> <span><?php echo e($category->courses_count); ?></span></a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                    <div class="sidebar-widget other-course-wedget">
                        <h3 class="sidebar-widget-title">Recent Courses</h3>
                        <div class="sidebar-widget-content">
                            <ul class="sidebar-widget-course list-unstyled">
                                <?php $__currentLoopData = $recentCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recentCourse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="single-course mb-3 d-flex">
                                    <div class="thumbnail me-3">
                                        <a href="<?php echo e(route('courseShow', $recentCourse->slug)); ?>" class="image">
                                            <img src="<?php echo e(asset('storage/' . $recentCourse->image)); ?>" alt="Course Image" class="img-fluid">
                                        </a>
                                    </div>
                                    <div class="info">
                                        <span class="price">$<?php echo e($recentCourse->price); ?><span>.00</span></span>
                                        <h6 class="title">
                                            <a href="<?php echo e(route('courseShow', $recentCourse->slug)); ?>">
                                                <?php echo e($recentCourse->title); ?>

                                            </a>
                                        </h6>
                                        <p class="course-description"><?php echo e($course->description); ?></p>

                                    </div>
                                </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>

                </div>
            </aside>
            <!-- End Course Sidebar -->
        </div>
    </div>
</section>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const stars = document.querySelectorAll('.star-rating .star');
    const ratingInput = document.getElementById('rating');

    stars.forEach(star => {
        star.addEventListener('click', function() {
            const rating = this.getAttribute('data-rating');
            ratingInput.value = rating;

            // Update visual state
            stars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });

        star.addEventListener('mouseover', function() {
            const rating = this.getAttribute('data-rating');
            stars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('hover');
                } else {
                    s.classList.remove('hover');
                }
            });
        });
    });

    // Initialize with default rating
    const defaultRating = ratingInput.value;
    stars.forEach((s, index) => {
        if (index < defaultRating) {
            s.classList.add('active');
        }
    });

    document.querySelector('.star-rating').addEventListener('mouseleave', function() {
        stars.forEach(s => s.classList.remove('hover'));
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\Sabeel-Ul-Quraan\resources\views/course-view.blade.php ENDPATH**/ ?>