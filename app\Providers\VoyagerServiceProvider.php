<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\CourseReview;
use App\Models\Testimonial;
use App\Models\TrialClass;
use App\Models\Contact;

class VoyagerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share unread counts with all Voyager views
        View::composer('voyager::*', function ($view) {
            $unreadCounts = [
                'course_reviews' => CourseReview::unread()->count(),
                'testimonials' => Testimonial::where('is_read', false)->count(),
                'trial_classes' => TrialClass::where('is_read', false)->count(),
                'contacts' => Contact::where('is_read', false)->count(),
            ];

            $view->with('unreadCounts', $unreadCounts);
        });
    }
}
