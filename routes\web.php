<?php
use TCG\Voyager\Facades\Voyager;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/', [App\Http\Controllers\Home::class, 'index'])->name('home');
Route::get('/testimonials', [App\Http\Controllers\Home::class, 'getTestimonial'])->name('testimonials');
Route::get('/courses', [App\Http\Controllers\Home::class, 'coursesPage'])->name('courses');
Route::get('/courses/{slug}', [App\Http\Controllers\Home::class, 'courseShow'])->name('courseShow');
Route::get('/courses/category/{slug}', [App\Http\Controllers\Home::class, 'filterByCategorycourse'])->name('category.filter.course');
Route::get('/team', [App\Http\Controllers\Home::class, 'teamPage'])->name('team');
Route::get('/policies', [App\Http\Controllers\Home::class, 'policiespage'])->name('policies');
Route::get('/blog', [App\Http\Controllers\Home::class, 'blogPage'])->name('blog');
Route::get('/posts/{slug}', [App\Http\Controllers\Home::class, 'postShow'])->name('postShow');
Route::get('/category/{slug}', [App\Http\Controllers\Home::class, 'filterByCategory'])->name('category.filter');
Route::get('/about', [App\Http\Controllers\Home::class, 'aboutPage'])->name('about');
Route::get('/trial-class', [App\Http\Controllers\Home::class, 'trialClasspage'])->name('trial-class');
Route::get('/pricing', [App\Http\Controllers\Home::class, 'pricingPage'])->name('pricing');
Route::post('/contact', [App\Http\Controllers\Home::class, 'storeContact'])->name('contact');
Route::get('/contact', function () {return view('contact');})->name('contact');
Route::post('/comments/store', [App\Http\Controllers\Home::class,'storeComment'])->name('comments.store');
Route::post('/trial-class/submit', [App\Http\Controllers\Home::class, 'submitTrial'])->name('trial-class.submit');
Route::post('/store-newsletter', [App\Http\Controllers\Home::class, 'storeNewsletter'])->name('storeNewsletter');
Route::post('/testimonials', [App\Http\Controllers\Home::class, 'storeTestimonial'])->name('testimonials.store');
Route::post('/course-reviews/store', [App\Http\Controllers\Home::class, 'storeCourseReview'])->name('course-reviews.store');
Route::get('/post/{id}/amp', [App\Http\Controllers\Home::class, 'showAmp'])->name('post.amp');

Route::group(['prefix' => 'admin'], function () {
    Voyager::routes();
});
