<!DOCTYPE html>
<html class="no-js" lang="zxx">

<!-- BEGIN HEAD -->
<head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <title>Sabeel Ul-Quraan</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/eggaEXlr.ico" />
    @yield('seo')

    <!-- Place favicon.ico in the root directory -->
    <meta property="og:image" content="{{ asset('storage/' . setting('site.logo')) }}" />

    <!-- Web Font -->
    <link
        href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
        rel="stylesheet">

    <!-- ========================= CSS here ========================= -->
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/css/LineIcons.2.0.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/css/tiny-slider.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/css/glightbox.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/css/main.css?id=1992') }}" />
    <style>
   .button-container {
    position: fixed;
    left: 35px; /* Adjust as needed */
    bottom: 20px; /* Adjust as needed */
    z-index: 9999; /* Ensure it's above other content */
}

.whatsapp-button {
    background-color: #25D366 !important; /* WhatsApp green color */
    color: white;
    padding: 10px 15px; /* Adjust padding for better look */
    border-radius: 50%; /* Make it circular */
    display: inline-block;
    text-decoration: none;
    font-size: 24px; /* Adjust the font size as needed */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add subtle shadow */
    transition: background-color 0.3s, transform 0.3s; /* Smooth transition */
}

.whatsapp-button:hover {
    background-color: #25D366 !important; /* Darker shade of WhatsApp green on hover */
    transform: scale(1.1); /* Slightly increase size on hover */
}

.lni-whatsapp {
    vertical-align: middle; /* Align icon vertically */
}
    </style>

    @yield('styles')
</head>
<!-- END HEAD -->

<body>
    <main>

		<!-- start header -->
		@include('layouts.header')
		<!-- end header -->


            <!-- start page content -->
			@yield('content')
			<!-- end page content -->
            @include('sweetalert::alert')

		<!-- start footer -->
		@include('layouts.footer')
		<!-- end footer -->

    </main><!-- Main Wrapper -->

    <!-- ========================= scroll-top ========================= -->
    <div class="button-container">
        <a href="https://wa.me/19497300077" target="_blank" class="whatsapp-button btn-hover">
            <i class="lni lni-whatsapp"></i>
        </a>
        <a href="#"  class="scroll-top btn-hover">
            <i class="lni lni-chevron-up"></i>
        </a>
    </div>
    @include('sweetalert::alert')

    <!-- ========================= JS here ========================= -->
<script src="{{ asset('assets/js/bootstrap.min.js') }}"></script>
<script src="{{ asset('assets/js/count-up.min.js') }}"></script>
<script src="{{ asset('assets/js/wow.min.js') }}"></script>
<script src="{{ asset('assets/js/tiny-slider.js') }}"></script>
<script src="{{ asset('assets/js/glightbox.min.js') }}"></script>
<script src="{{ asset('assets/js/main.js') }}"></script>
<script src="{{ asset('assets/js/jquery-counterup.min.js') }}"></script>


<script type="text/javascript"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        //========= Hero Slider
        tns({
            container: '.hero-slider',
            items: 1,
            slideBy: 'page',
            autoplay: false,
            mouseDrag: true,
            gutter: 0,
            nav: true,
            controls: false,
            controlsText: ['<i class="lni lni-arrow-left"></i>', '<i class="lni lni-arrow-right"></i>']
        });
    });
</script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        //========= Testimonial Slider
        tns({
            container: '.testimonial-slider',
            items: 3,
            slideBy: 'page',
            autoplay: false,
            mouseDrag: true,
            gutter: 0,
            nav: true,
            controls: false,
            controlsText: ['<i class="lni lni-arrow-left"></i>', '<i class="lni lni-arrow-right"></i>'],
            responsive: {
                0: {
                    items: 1
                },
                540: {
                    items: 1
                },
                768: {
                    items: 2
                },
                992: {
                    items: 2
                },
                1170: {
                    items: 3
                }
            }
        });
    });
</script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        //====== Clients Logo Slider
        tns({
            container: '.client-logo-carousel',
            slideBy: 'page',
            autoplay: true,
            autoplayButtonOutput: false,
            mouseDrag: true,
            gutter: 15,
            nav: false,
            controls: false,
            responsive: {
                0: {
                    items: 1
                },
                540: {
                    items: 3
                },
                768: {
                    items: 4
                },
                992: {
                    items: 4
                },
                1170: {
                    items: 6
                }
            }
        });
    });
</script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        //========= GLightbox
        GLightbox({
            href: 'https://www.youtube.com/watch?v=uKJXm4zkS0g',
            type: 'video',
            source: 'youtube', // vimeo, youtube or local
            width: 900,
            autoplayVideos: true
        });
    });
</script>

    @yield('scripts')
</body>

</html>
