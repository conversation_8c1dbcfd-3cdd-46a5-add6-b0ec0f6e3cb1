<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use TCG\Voyager\Http\Controllers\VoyagerBaseController;
use App\Models\TrialClass;

class TrialClassController extends VoyagerBaseController
{
    public function show(Request $request, $id)
    {
        // Mark as read when viewing
        $trialClass = TrialClass::findOrFail($id);
        if (!$trialClass->is_read) {
            $trialClass->update(['is_read' => true]);
        }

        return parent::show($request, $id);
    }

    public function edit(Request $request, $id)
    {
        // Mark as read when editing
        $trialClass = TrialClass::findOrFail($id);
        if (!$trialClass->is_read) {
            $trialClass->update(['is_read' => true]);
        }

        return parent::edit($request, $id);
    }
}
