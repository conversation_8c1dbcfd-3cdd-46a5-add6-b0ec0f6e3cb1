{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "416b7bea5836409f30d381bebd2be902", "packages": [{"name": "arrilot/laravel-widgets", "version": "3.14.0", "source": {"type": "git", "url": "https://github.com/arrilot/laravel-widgets.git", "reference": "99419f5b6190825733c731bd3b44f49fdc56a6ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/arrilot/laravel-widgets/zipball/99419f5b6190825733c731bd3b44f49fdc56a6ad", "reference": "99419f5b6190825733c731bd3b44f49fdc56a6ad", "shasum": ""}, "require": {"illuminate/cache": ">=9", "illuminate/console": ">=9", "illuminate/container": ">=9", "illuminate/contracts": ">=9", "illuminate/routing": ">=9", "illuminate/support": ">=9", "illuminate/view": ">=9", "php": ">=7.4"}, "require-dev": {"nunomaduro/larastan": "^2.6", "phpunit/phpunit": "~8.0"}, "type": "library", "extra": {"laravel": {"providers": ["Arrilot\\Widgets\\ServiceProvider"], "aliases": {"Widget": "Arrilot\\Widgets\\Facade", "AsyncWidget": "Arrilot\\Widgets\\AsyncFacade"}}}, "autoload": {"psr-4": {"Arrilot\\Widgets\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A powerful alternative to view composers. Asynchronous widgets, reloadable widgets, console generator, caching - everything you can think of.", "homepage": "https://github.com/arrilot/laravel-widgets", "keywords": ["ajax", "laravel", "widgets"], "support": {"issues": "https://github.com/arrilot/laravel-widgets/issues", "source": "https://github.com/arrilot/laravel-widgets/tree/3.14.0"}, "time": "2023-11-19T18:27:27+00:00"}, {"name": "brick/math", "version": "0.12.1", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "f510c0a40911935b77b86859eb5223d58d660df1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/f510c0a40911935b77b86859eb5223d58d660df1", "reference": "f510c0a40911935b77b86859eb5223d58d660df1", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "5.16.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.1"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-11-29T23:19:16+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"doctrine/dbal": "<3.7.0 || >=4.0.0"}, "require-dev": {"doctrine/dbal": "^3.7.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/2.1.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2023-12-11T17:09:12+00:00"}, {"name": "dflydev/dot-access-data", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "f41715465d65213d644d3141a6a93081be5d3549"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/f41715465d65213d644d3141a6a93081be5d3549", "reference": "f41715465d65213d644d3141a6a93081be5d3549", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.2"}, "time": "2022-10-27T11:44:00+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/dbal", "version": "3.8.4", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "b05e48a745f722801f55408d0dbd8003b403dbbd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/b05e48a745f722801f55408d0dbd8003b403dbbd", "reference": "b05e48a745f722801f55408d0dbd8003b403dbbd", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.1", "phpstan/phpstan": "1.10.58", "phpstan/phpstan-strict-rules": "^1.5", "phpunit/phpunit": "9.6.16", "psalm/plugin-phpunit": "0.18.4", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.9.0", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/console": "^4.4|^5.4|^6.0|^7.0", "vimeo/psalm": "4.30.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.8.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2024-04-25T07:04:44+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "750671534e0241a7c50ea5b43f67e23eb5c96f32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/750671534e0241a7c50ea5b43f67e23eb5c96f32", "reference": "750671534e0241a7c50ea5b43f67e23eb5c96f32", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^4.28"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:59:15+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-webmozart-assert": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.3"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2023-08-10T19:36:49+00:00"}, {"name": "egulias/email-validator", "version": "4.0.2", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ebaaf5be6c0286928352e054f2d5125608e5405e", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.2"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-10-06T06:47:41+00:00"}, {"name": "fruitcake/php-cors", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/fruitcake/php-cors.git", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/php-cors/zipball/3d158f36e7875e2f040f37bc0573956240a5a38b", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b", "shasum": ""}, "require": {"php": "^7.4|^8.0", "symfony/http-foundation": "^4.4|^5.4|^6|^7"}, "require-dev": {"phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barryvdh", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library for the Symfony HttpFoundation", "homepage": "https://github.com/fruitcake/php-cors", "keywords": ["cors", "laravel", "symfony"], "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.3.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2023-10-12T05:21:21+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "fbd48bce38f73f8a4ec8583362e732e4095e5862"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/fbd48bce38f73f8a4ec8583362e732e4095e5862", "reference": "fbd48bce38f73f8a4ec8583362e732e4095e5862", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.2"}, "require-dev": {"phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2023-11-12T22:16:48+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-12-03T20:35:24+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/bbff78d96034045e58e13dedd6ad91b5d1253223", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-12-03T20:19:20+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.6.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/45b30f99ac27b5ca93cb4831afe16285f57b8221", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-12-03T20:05:35+00:00"}, {"name": "guzzlehttp/uri-template", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/guzzle/uri-template.git", "reference": "ecea8feef63bd4fef1f037ecb288386999ecc11c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/uri-template/zipball/ecea8feef63bd4fef1f037ecb288386999ecc11c", "reference": "ecea8feef63bd4fef1f037ecb288386999ecc11c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "uri-template/tests": "1.0.0"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\UriTemplate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "A polyfill class for uri_template of PHP", "keywords": ["guzzlehttp", "uri-template"], "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/uri-template", "type": "tidelift"}], "time": "2023-12-03T19:50:20+00:00"}, {"name": "intervention/image", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "04be355f8d6734c826045d02a1079ad658322dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/04be355f8d6734c826045d02a1079ad658322dad", "reference": "04be355f8d6734c826045d02a1079ad658322dad", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/2.7.2"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "time": "2022-05-21T17:30:32+00:00"}, {"name": "laravel/framework", "version": "v10.48.10", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "91e2b9e218afa4e5c377510faa11957042831ba3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/91e2b9e218afa4e5c377510faa11957042831ba3", "reference": "91e2b9e218afa4e5c377510faa11957042831ba3", "shasum": ""}, "require": {"brick/math": "^0.9.3|^0.10.2|^0.11|^0.12", "composer-runtime-api": "^2.2", "doctrine/inflector": "^2.0.5", "dragonmantank/cron-expression": "^3.3.2", "egulias/email-validator": "^3.2.1|^4.0", "ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-session": "*", "ext-tokenizer": "*", "fruitcake/php-cors": "^1.2", "guzzlehttp/uri-template": "^1.0", "laravel/prompts": "^0.1.9", "laravel/serializable-closure": "^1.3", "league/commonmark": "^2.2.1", "league/flysystem": "^3.8.0", "monolog/monolog": "^3.0", "nesbot/carbon": "^2.67", "nunomaduro/termwind": "^1.13", "php": "^8.1", "psr/container": "^1.1.1|^2.0.1", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "ramsey/uuid": "^4.7", "symfony/console": "^6.2", "symfony/error-handler": "^6.2", "symfony/finder": "^6.2", "symfony/http-foundation": "^6.4", "symfony/http-kernel": "^6.2", "symfony/mailer": "^6.2", "symfony/mime": "^6.2", "symfony/process": "^6.2", "symfony/routing": "^6.2", "symfony/uid": "^6.2", "symfony/var-dumper": "^6.2", "tijsverkoyen/css-to-inline-styles": "^2.2.5", "vlucas/phpdotenv": "^5.4.1", "voku/portable-ascii": "^2.0"}, "conflict": {"carbonphp/carbon-doctrine-types": ">=3.0", "doctrine/dbal": ">=4.0", "mockery/mockery": "1.6.8", "phpunit/phpunit": ">=11.0.0", "tightenco/collect": "<5.5.33"}, "provide": {"psr/container-implementation": "1.1|2.0", "psr/simple-cache-implementation": "1.0|2.0|3.0"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/collections": "self.version", "illuminate/conditionable": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/macroable": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/process": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/testing": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"ably/ably-php": "^1.0", "aws/aws-sdk-php": "^3.235.5", "doctrine/dbal": "^3.5.1", "ext-gmp": "*", "fakerphp/faker": "^1.21", "guzzlehttp/guzzle": "^7.5", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-ftp": "^3.0", "league/flysystem-path-prefixing": "^3.3", "league/flysystem-read-only": "^3.3", "league/flysystem-sftp-v3": "^3.0", "mockery/mockery": "^1.5.1", "nyholm/psr7": "^1.2", "orchestra/testbench-core": "^8.23.4", "pda/pheanstalk": "^4.0", "phpstan/phpstan": "^1.4.7", "phpunit/phpunit": "^10.0.7", "predis/predis": "^2.0.2", "symfony/cache": "^6.2", "symfony/http-client": "^6.2.4", "symfony/psr-http-message-bridge": "^2.0"}, "suggest": {"ably/ably-php": "Required to use the Ably broadcast driver (^1.0).", "aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage, and SES mail driver (^3.235.5).", "brianium/paratest": "Required to run tests in parallel (^6.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^3.5.1).", "ext-apcu": "Required to use the APC cache driver.", "ext-fileinfo": "Required to use the Filesystem class.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker and console signal trapping.", "ext-pdo": "Required to use all database features.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.14.3).", "guzzlehttp/guzzle": "Required to use the HTTP Client and the ping methods on schedules (^7.5).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.0).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.0).", "league/flysystem-path-prefixing": "Required to use the scoped driver (^3.3).", "league/flysystem-read-only": "Required to use read-only disks (^3.3)", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.0).", "mockery/mockery": "Required to use mocking (^1.5.1).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "phpunit/phpunit": "Required to use assertions and run tests (^9.5.8|^10.0.7).", "predis/predis": "Required to use the predis connector (^2.0.2).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^6.0|^7.0).", "symfony/cache": "Required to PSR-6 cache bridge (^6.2).", "symfony/filesystem": "Required to enable support for relative symbolic links (^6.2).", "symfony/http-client": "Required to enable support for the Symfony API mail transports (^6.2).", "symfony/mailgun-mailer": "Required to enable support for the Mailgun mail transport (^6.2).", "symfony/postmark-mailer": "Required to enable support for the Postmark mail transport (^6.2).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "autoload": {"files": ["src/Illuminate/Collections/helpers.php", "src/Illuminate/Events/functions.php", "src/Illuminate/Filesystem/functions.php", "src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/", "Illuminate\\Support\\": ["src/Illuminate/Macroable/", "src/Illuminate/Collections/", "src/Illuminate/Conditionable/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2024-04-30T12:52:59+00:00"}, {"name": "laravel/prompts", "version": "v0.1.21", "source": {"type": "git", "url": "https://github.com/laravel/prompts.git", "reference": "23ea808e8a145653e0ab29e30d4385e49f40a920"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/prompts/zipball/23ea808e8a145653e0ab29e30d4385e49f40a920", "reference": "23ea808e8a145653e0ab29e30d4385e49f40a920", "shasum": ""}, "require": {"ext-mbstring": "*", "illuminate/collections": "^10.0|^11.0", "php": "^8.1", "symfony/console": "^6.2|^7.0"}, "conflict": {"illuminate/console": ">=10.17.0 <10.25.0", "laravel/framework": ">=10.17.0 <10.25.0"}, "require-dev": {"mockery/mockery": "^1.5", "pestphp/pest": "^2.3", "phpstan/phpstan": "^1.11", "phpstan/phpstan-mockery": "^1.1"}, "suggest": {"ext-pcntl": "Required for the spinner to be animated."}, "type": "library", "extra": {"branch-alias": {"dev-main": "0.1.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Laravel\\Prompts\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Add beautiful and user-friendly forms to your command-line applications.", "support": {"issues": "https://github.com/laravel/prompts/issues", "source": "https://github.com/laravel/prompts/tree/v0.1.21"}, "time": "2024-04-30T12:46:16+00:00"}, {"name": "laravel/sanctum", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/laravel/sanctum.git", "reference": "8c104366459739f3ada0e994bcd3e6fd681ce3d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/sanctum/zipball/8c104366459739f3ada0e994bcd3e6fd681ce3d5", "reference": "8c104366459739f3ada0e994bcd3e6fd681ce3d5", "shasum": ""}, "require": {"ext-json": "*", "illuminate/console": "^9.21|^10.0", "illuminate/contracts": "^9.21|^10.0", "illuminate/database": "^9.21|^10.0", "illuminate/support": "^9.21|^10.0", "php": "^8.0.2"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^7.28.2|^8.8.3", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}, "laravel": {"providers": ["Laravel\\Sanctum\\SanctumServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Sanctum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel Sanctum provides a featherweight authentication system for SPAs and simple APIs.", "keywords": ["auth", "laravel", "sanctum"], "support": {"issues": "https://github.com/laravel/sanctum/issues", "source": "https://github.com/laravel/sanctum"}, "time": "2023-12-19T18:44:48+00:00"}, {"name": "laravel/serializable-closure", "version": "v1.3.3", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "3dbf8a8e914634c48d389c1234552666b3d43754"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/3dbf8a8e914634c48d389c1234552666b3d43754", "reference": "3dbf8a8e914634c48d389c1234552666b3d43754", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"nesbot/carbon": "^2.61", "pestphp/pest": "^1.21.3", "phpstan/phpstan": "^1.8.2", "symfony/var-dumper": "^5.4.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "time": "2023-11-08T14:08:06+00:00"}, {"name": "laravel/tinker", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "502e0fe3f0415d06d5db1f83a472f0f3b754bafe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/502e0fe3f0415d06d5db1f83a472f0f3b754bafe", "reference": "502e0fe3f0415d06d5db1f83a472f0f3b754bafe", "shasum": ""}, "require": {"illuminate/console": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "php": "^7.2.5|^8.0", "psy/psysh": "^0.11.1|^0.12.0", "symfony/var-dumper": "^4.3.4|^5.0|^6.0|^7.0"}, "require-dev": {"mockery/mockery": "~1.3.3|^1.4.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "suggest": {"illuminate/database": "The Illuminate Database package (^6.0|^7.0|^8.0|^9.0|^10.0|^11.0)."}, "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "support": {"issues": "https://github.com/laravel/tinker/issues", "source": "https://github.com/laravel/tinker/tree/v2.9.0"}, "time": "2024-01-04T16:10:04+00:00"}, {"name": "laravel/ui", "version": "v4.5.1", "source": {"type": "git", "url": "https://github.com/laravel/ui.git", "reference": "a3562953123946996a503159199d6742d5534e61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/ui/zipball/a3562953123946996a503159199d6742d5534e61", "reference": "a3562953123946996a503159199d6742d5534e61", "shasum": ""}, "require": {"illuminate/console": "^9.21|^10.0|^11.0", "illuminate/filesystem": "^9.21|^10.0|^11.0", "illuminate/support": "^9.21|^10.0|^11.0", "illuminate/validation": "^9.21|^10.0|^11.0", "php": "^8.0", "symfony/console": "^6.0|^7.0"}, "require-dev": {"orchestra/testbench": "^7.35|^8.15|^9.0", "phpunit/phpunit": "^9.3|^10.4|^11.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}, "laravel": {"providers": ["Laravel\\Ui\\UiServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Ui\\": "src/", "Illuminate\\Foundation\\Auth\\": "auth-backend/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel UI utilities and presets.", "keywords": ["laravel", "ui"], "support": {"source": "https://github.com/laravel/ui/tree/v4.5.1"}, "time": "2024-03-21T18:12:29+00:00"}, {"name": "league/commonmark", "version": "2.4.2", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "91c24291965bd6d7c46c46a12ba7492f83b1cadf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/91c24291965bd6d7c46c46a12ba7492f83b1cadf", "reference": "91c24291965bd6d7c46c46a12ba7492f83b1cadf", "shasum": ""}, "require": {"ext-mbstring": "*", "league/config": "^1.1.1", "php": "^7.4 || ^8.0", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"cebe/markdown": "^1.0", "commonmark/cmark": "0.30.3", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0 || ^7.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 || ^7.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}, "suggest": {"symfony/yaml": "v2.3+ required if using the Front Matter extension"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and GitHub-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "forum": "https://github.com/thephpleague/commonmark/discussions", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2024-02-02T11:59:32+00:00"}, {"name": "league/config", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/config.git", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/config/zipball/754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3.0.1", "nette/schema": "^1.2", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.2-dev"}}, "autoload": {"psr-4": {"League\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Define configuration arrays with strict schemas and access values with dot notation", "homepage": "https://config.thephpleague.com", "keywords": ["array", "config", "configuration", "dot", "dot-access", "nested", "schema"], "support": {"docs": "https://config.thephpleague.com/", "issues": "https://github.com/thephpleague/config/issues", "rss": "https://github.com/thephpleague/config/releases.atom", "source": "https://github.com/thephpleague/config"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}], "time": "2022-12-11T20:36:23+00:00"}, {"name": "league/flysystem", "version": "3.27.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "4729745b1ab737908c7d055148c9a6b3e959832f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/4729745b1ab737908c7d055148c9a6b3e959832f", "reference": "4729745b1ab737908c7d055148c9a6b3e959832f", "shasum": ""}, "require": {"league/flysystem-local": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"async-aws/core": "<1.19.0", "async-aws/s3": "<1.14.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1", "phpseclib/phpseclib": "3.0.15", "symfony/http-client": "<5.2"}, "require-dev": {"async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "microsoft/azure-storage-blob": "^1.1", "phpseclib/phpseclib": "^3.0.36", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5.11|^10.0", "sabre/dav": "^4.6.0"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.27.0"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}], "time": "2024-04-07T19:17:50+00:00"}, {"name": "league/flysystem-local", "version": "3.25.1", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-local.git", "reference": "61a6a90d6e999e4ddd9ce5adb356de0939060b92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/61a6a90d6e999e4ddd9ce5adb356de0939060b92", "reference": "61a6a90d6e999e4ddd9ce5adb356de0939060b92", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/flysystem": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Local\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Local filesystem adapter for Flysystem.", "keywords": ["Flysystem", "file", "files", "filesystem", "local"], "support": {"source": "https://github.com/thephpleague/flysystem-local/tree/3.25.1"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}], "time": "2024-03-15T19:58:44+00:00"}, {"name": "league/iso3166", "version": "4.3.1", "source": {"type": "git", "url": "https://github.com/thephpleague/iso3166.git", "reference": "11703e0313f34920add11c0228f0dd43ebd10f9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/iso3166/zipball/11703e0313f34920add11c0228f0dd43ebd10f9a", "reference": "11703e0313f34920add11c0228f0dd43ebd10f9a", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.3|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"League\\ISO3166\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "ISO 3166-1 PHP Library", "homepage": "https://github.com/thephpleague/iso3166", "keywords": ["3166", "3166-1", "ISO 3166", "countries", "iso", "library"], "support": {"issues": "https://github.com/thephpleague/iso3166/issues", "source": "https://github.com/thephpleague/iso3166"}, "time": "2023-09-11T07:59:36+00:00"}, {"name": "league/mime-type-detection", "version": "1.15.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.15.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-01-28T23:22:08+00:00"}, {"name": "monolog/monolog", "version": "3.6.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "4b18b21a5527a3d5ffdac2fd35d3ab25a9597654"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/4b18b21a5527a3d5ffdac2fd35d3ab25a9597654", "reference": "4b18b21a5527a3d5ffdac2fd35d3ab25a9597654", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpstan/phpstan": "^1.9", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-strict-rules": "^1.4", "phpunit/phpunit": "^10.5.17", "predis/predis": "^1.1 || ^2", "ruflin/elastica": "^7", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.6.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2024-04-12T21:02:21+00:00"}, {"name": "nesbot/carbon", "version": "2.72.3", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "0c6fd108360c562f6e4fd1dedb8233b423e91c83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/0c6fd108360c562f6e4fd1dedb8233b423e91c83", "reference": "0c6fd108360c562f6e4fd1dedb8233b423e91c83", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2024-01-25T10:35:09+00:00"}, {"name": "nette/schema", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "a6d3a6d1f545f01ef38e60f375d1cf1f4de98188"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/a6d3a6d1f545f01ef38e60f375d1cf1f4de98188", "reference": "a6d3a6d1f545f01ef38e60f375d1cf1f4de98188", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.3"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.3.0"}, "time": "2023-12-11T11:54:22+00:00"}, {"name": "nette/utils", "version": "v4.0.4", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "shasum": ""}, "require": {"php": ">=8.0 <8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.4"}, "time": "2024-01-17T16:50:36+00:00"}, {"name": "nikic/php-parser", "version": "v5.0.2", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "139676794dc1e9231bf7bcd123cfc0c99182cb13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/139676794dc1e9231bf7bcd123cfc0c99182cb13", "reference": "139676794dc1e9231bf7bcd123cfc0c99182cb13", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.0.2"}, "time": "2024-03-05T20:51:40+00:00"}, {"name": "nunomaduro/termwind", "version": "v1.15.1", "source": {"type": "git", "url": "https://github.com/nunomaduro/termwind.git", "reference": "8ab0b32c8caa4a2e09700ea32925441385e4a5dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/termwind/zipball/8ab0b32c8caa4a2e09700ea32925441385e4a5dc", "reference": "8ab0b32c8caa4a2e09700ea32925441385e4a5dc", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^8.0", "symfony/console": "^5.3.0|^6.0.0"}, "require-dev": {"ergebnis/phpstan-rules": "^1.0.", "illuminate/console": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "laravel/pint": "^1.0.0", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^1.4.6", "phpstan/phpstan-strict-rules": "^1.1.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}, "type": "library", "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Termwind\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Its like Tailwind CSS, but for the console.", "keywords": ["cli", "console", "css", "package", "php", "style"], "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.15.1"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://github.com/xiCO2k", "type": "github"}], "time": "2023-02-08T01:06:31+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.2", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "80735db690fe4fc5c76dfa7f9b770634285fa820"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/80735db690fe4fc5c76dfa7f9b770634285fa820", "reference": "80735db690fe4fc5c76dfa7f9b770634285fa820", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2023-11-12T21:59:55+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "psy/psysh", "version": "v0.12.3", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "b6b6cce7d3ee8fbf31843edce5e8f5a72eff4a73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/b6b6cce7d3ee8fbf31843edce5e8f5a72eff4a73", "reference": "b6b6cce7d3ee8fbf31843edce5e8f5a72eff4a73", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-main": "0.12.x-dev"}, "bamarni-bin": {"bin-links": false, "forward-command": false}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.3"}, "time": "2024-04-02T15:57:53+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5", "reference": "a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.0.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2022-12-31T21:50:55+00:00"}, {"name": "ramsey/uuid", "version": "4.7.6", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "91039bc1faa45ba123c4328958e620d382ec7088"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/91039bc1faa45ba123c4328958e620d382ec7088", "reference": "91039bc1faa45ba123c4328958e620d382ec7088", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.6"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2024-04-27T21:32:50+00:00"}, {"name": "realrashid/sweet-alert", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/realrashid/sweet-alert.git", "reference": "2d38720644fb30ff6db4e2a5ee013c09057b1f3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/realrashid/sweet-alert/zipball/2d38720644fb30ff6db4e2a5ee013c09057b1f3b", "reference": "2d38720644fb30ff6db4e2a5ee013c09057b1f3b", "shasum": ""}, "require": {"laravel/framework": "^5.6|^6.0|^7.0|^8.0|^9.0|^9.11|9.14.*|^10.0|^11.0", "php": "^7.2|^8.0|^8.1"}, "require-dev": {"symfony/thanks": "^1.0"}, "type": "library", "extra": {"laravel": {"providers": ["RealRashid\\SweetAlert\\SweetAlertServiceProvider"], "aliases": {"Alert": "RealRashid\\SweetAlert\\Facades\\Alert"}}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"RealRashid\\SweetAlert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://realrashid.com", "role": "Developer"}], "description": "Laravel Sweet Alert Is A Package For Laravel Provides An Easy Way To Display Alert Messages Using The SweetAlert2 Library.", "homepage": "https://github.com/realrashid/sweet-alert", "keywords": ["alert", "laravel", "laravel-package", "notifier", "noty", "sweet-alert", "sweet-alert2", "toast"], "support": {"docs": "https://realrashid.github.io/sweet-alert/", "email": "<EMAIL>", "issues": "https://github.com/realrashid/sweet-alert/issues", "source": "https://github.com/realrashid/sweet-alert"}, "funding": [{"url": "https://ko-fi.com/realrashid", "type": "custom"}, {"url": "https://www.buymeacoffee.com/realrashid", "type": "custom"}, {"url": "https://issuehunt.io/r/realrashid", "type": "issuehunt"}, {"url": "https://tidelift.com/funding/github/packagist/realrashid/sweet-alert", "type": "tidelift"}], "time": "2024-03-02T08:59:25+00:00"}, {"name": "symfony/console", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "a170e64ae10d00ba89e2acbb590dc2e54da8ad8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/a170e64ae10d00ba89e2acbb590dc2e54da8ad8f", "reference": "a170e64ae10d00ba89e2acbb590dc2e54da8ad8f", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/css-selector", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "1c5d5c2103c3762aff27a27e1e2409e30a79083b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/1c5d5c2103c3762aff27a27e1e2409e30a79083b", "reference": "1c5d5c2103c3762aff27a27e1e2409e30a79083b", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/error-handler", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "667a072466c6a53827ed7b119af93806b884cbb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/667a072466c6a53827ed7b119af93806b884cbb3", "reference": "667a072466c6a53827ed7b119af93806b884cbb3", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "d84384f3f67de3cb650db64d685d70395dacfc3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/d84384f3f67de3cb650db64d685d70395dacfc3f", "reference": "d84384f3f67de3cb650db64d685d70395dacfc3f", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "8f93aec25d41b72493c6ddff14e916177c9efc50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/8f93aec25d41b72493c6ddff14e916177c9efc50", "reference": "8f93aec25d41b72493c6ddff14e916177c9efc50", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/finder", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "511c48990be17358c23bf45c5d71ab85d40fb764"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/511c48990be17358c23bf45c5d71ab85d40fb764", "reference": "511c48990be17358c23bf45c5d71ab85d40fb764", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-23T10:36:43+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "b4db6b833035477cb70e18d0ae33cb7c2b521759"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/b4db6b833035477cb70e18d0ae33cb7c2b521759", "reference": "b4db6b833035477cb70e18d0ae33cb7c2b521759", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.3"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.3|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/http-kernel", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "b7b5e6cdef670a0c82d015a966ffc7e855861a98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/b7b5e6cdef670a0c82d015a966ffc7e855861a98", "reference": "b7b5e6cdef670a0c82d015a966ffc7e855861a98", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.4", "symfony/config": "<6.1", "symfony/console": "<5.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/translation": "<5.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<5.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.3", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/clock": "^6.2|^7.0", "symfony/config": "^6.1|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.5|^6.0.5|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.4|^7.0.4", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.4|^7.0", "symfony/var-exporter": "^6.2|^7.0", "twig/twig": "^2.13|^3.0.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-29T11:24:44+00:00"}, {"name": "symfony/mailer", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "2c446d4e446995bed983c0b5bb9ff837e8de7dbd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/2c446d4e446995bed983c0b5bb9ff837e8de7dbd", "reference": "2c446d4e446995bed983c0b5bb9ff837e8de7dbd", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.1", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/mime", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "decadcf3865918ecfcbfa90968553994ce935a5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/decadcf3865918ecfcbfa90968553994ce935a5e", "reference": "decadcf3865918ecfcbfa90968553994ce935a5e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.3.2"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.4|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.3.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ef4d7e442ca910c4764bce785146269b30cb5fc4", "reference": "ef4d7e442ca910c4764bce785146269b30cb5fc4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/32a9da87d7b3245e09ac426c83d334ae9f06f80f", "reference": "32a9da87d7b3245e09ac426c83d334ae9f06f80f", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "a287ed7475f85bf6f61890146edbc932c0fff919"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a287ed7475f85bf6f61890146edbc932c0fff919", "reference": "a287ed7475f85bf6f61890146edbc932c0fff919", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/bc45c394692b948b4d383a08d7753968bed9a83d", "reference": "bc45c394692b948b4d383a08d7753968bed9a83d", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "reference": "9773676c8a1bb1f8d4340a62efe641cf76eda7ec", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "861391a8da9a04cbad2d232ddd9e4893220d6e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/861391a8da9a04cbad2d232ddd9e4893220d6e25", "reference": "861391a8da9a04cbad2d232ddd9e4893220d6e25", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "reference": "87b68208d5c1188808dd7839ee1e6c8ec3b02f1b", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/86fcae159633351e5fd145d1c47de6c528f8caff", "reference": "86fcae159633351e5fd145d1c47de6c528f8caff", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-php80": "^1.14"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.29.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "3abdd21b0ceaa3000ee950097bc3cf9efc137853"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/3abdd21b0ceaa3000ee950097bc3cf9efc137853", "reference": "3abdd21b0ceaa3000ee950097bc3cf9efc137853", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.29.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-29T20:11:03+00:00"}, {"name": "symfony/process", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "cdb1c81c145fd5aa9b0038bab694035020943381"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/cdb1c81c145fd5aa9b0038bab694035020943381", "reference": "cdb1c81c145fd5aa9b0038bab694035020943381", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/routing", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "276e06398f71fa2a973264d94f28150f93cfb907"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/276e06398f71fa2a973264d94f28150f93cfb907", "reference": "276e06398f71fa2a973264d94f28150f93cfb907", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<6.2", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^6.2|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/string", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "ffeb9591c61f65a68d47f77d12b83fa530227a69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/ffeb9591c61f65a68d47f77d12b83fa530227a69", "reference": "ffeb9591c61f65a68d47f77d12b83fa530227a69", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/intl": "^6.2|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/translation", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "7495687c58bfd88b7883823747b0656d90679123"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/7495687c58bfd88b7883823747b0656d90679123", "reference": "7495687c58bfd88b7883823747b0656d90679123", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "b9d2189887bb6b2e0367a9fc7136c5239ab9b05a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/b9d2189887bb6b2e0367a9fc7136c5239ab9b05a", "reference": "b9d2189887bb6b2e0367a9fc7136c5239ab9b05a", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/uid", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/uid.git", "reference": "a66efcb71d8bc3a207d9d78e0bd67f3321510355"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/uid/zipball/a66efcb71d8bc3a207d9d78e0bd67f3321510355", "reference": "a66efcb71d8bc3a207d9d78e0bd67f3321510355", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to generate and represent UIDs", "homepage": "https://symfony.com", "keywords": ["UID", "ulid", "uuid"], "support": {"source": "https://github.com/symfony/uid/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "7a9cd977cd1c5fed3694bee52990866432af07d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/7a9cd977cd1c5fed3694bee52990866432af07d7", "reference": "7a9cd977cd1c5fed3694bee52990866432af07d7", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:22:46+00:00"}, {"name": "tcg/voyager", "version": "v1.7", "source": {"type": "git", "url": "https://github.com/thedevdojo/voyager.git", "reference": "19286d193d7f9095d088b743cdd5e7d9649b0855"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thedevdojo/voyager/zipball/19286d193d7f9095d088b743cdd5e7d9649b0855", "reference": "19286d193d7f9095d088b743cdd5e7d9649b0855", "shasum": ""}, "require": {"arrilot/laravel-widgets": "^3.7", "doctrine/dbal": "^3.1|^3.5", "ext-json": "*", "illuminate/support": "~8.0|~9.0|~10.0", "intervention/image": "^2.7", "laravel/ui": ">=1.0", "league/flysystem": "~1.1|~2.0|~3.0", "php": "^7.3|^7.4|^8.0|^8.1|^8.2"}, "require-dev": {"laravel/browser-kit-testing": ">=6.1.0", "laravel/framework": "~8.0|~9.0|~10.0", "orchestra/testbench": ">=6.0", "orchestra/testbench-browser-kit": ">=4.0", "phpunit/phpcov": ">=6.0", "phpunit/phpunit": ">=8.0"}, "type": "library", "extra": {"laravel": {"providers": ["TCG\\Voyager\\VoyagerServiceProvider", "TCG\\Voyager\\Providers\\VoyagerDummyServiceProvider"]}}, "autoload": {"psr-4": {"TCG\\Voyager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Laravel Admin Package for The Control Group to make your life easier and steer your project in the right direction", "homepage": "https://voyager.devdojo.com/", "keywords": ["admin", "laravel", "panel"], "support": {"issues": "https://github.com/the-control-group/voyager/issues", "source": "https://github.com/the-control-group/voyager"}, "funding": [{"url": "https://github.com/emptynick", "type": "github"}, {"url": "https://github.com/fletch3555", "type": "github"}, {"url": "https://github.com/tnylea", "type": "github"}], "time": "2023-11-10T16:29:12+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.2.7", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/83ee6f38df0a63106a9e4536e3060458b74ccedb", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.2.7"}, "time": "2023-12-08T13:03:43+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.0", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4", "reference": "2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.2", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.2", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2023-11-12T22:43:29+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b56450eed252f6801410d810c8e1727224ae0743"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b56450eed252f6801410d810c8e1727224ae0743", "reference": "b56450eed252f6801410d810c8e1727224ae0743", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.1"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2022-03-08T17:03:00+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "fakerphp/faker", "version": "v1.23.1", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "bfb4fe148adbf78eff521199619b93a52ae3554b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/bfb4fe148adbf78eff521199619b93a52ae3554b", "reference": "bfb4fe148adbf78eff521199619b93a52ae3554b", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.23.1"}, "time": "2024-01-02T13:46:09+00:00"}, {"name": "filp/whoops", "version": "2.15.4", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "a139776fa3f5985a50b509f2a02ff0f709d2a546"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/a139776fa3f5985a50b509f2a02ff0f709d2a546", "reference": "a139776fa3f5985a50b509f2a02ff0f709d2a546", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.15.4"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2023-11-03T12:00:00+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "laravel/pint", "version": "v1.15.3", "source": {"type": "git", "url": "https://github.com/laravel/pint.git", "reference": "3600b5d17aff52f6100ea4921849deacbbeb8656"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/pint/zipball/3600b5d17aff52f6100ea4921849deacbbeb8656", "reference": "3600b5d17aff52f6100ea4921849deacbbeb8656", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "ext-xml": "*", "php": "^8.1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.54.0", "illuminate/view": "^10.48.8", "larastan/larastan": "^2.9.5", "laravel-zero/framework": "^10.3.0", "mockery/mockery": "^1.6.11", "nunomaduro/termwind": "^1.15.1", "pestphp/pest": "^2.34.7"}, "bin": ["builds/pint"], "type": "project", "autoload": {"psr-4": {"App\\": "app/", "Database\\Seeders\\": "database/seeders/", "Database\\Factories\\": "database/factories/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An opinionated code formatter for PHP.", "homepage": "https://laravel.com", "keywords": ["format", "formatter", "lint", "linter", "php"], "support": {"issues": "https://github.com/laravel/pint/issues", "source": "https://github.com/laravel/pint"}, "time": "2024-04-30T15:02:26+00:00"}, {"name": "laravel/sail", "version": "v1.29.1", "source": {"type": "git", "url": "https://github.com/laravel/sail.git", "reference": "8be4a31150eab3b46af11a2e7b2c4632eefaad7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/sail/zipball/8be4a31150eab3b46af11a2e7b2c4632eefaad7e", "reference": "8be4a31150eab3b46af11a2e7b2c4632eefaad7e", "shasum": ""}, "require": {"illuminate/console": "^9.52.16|^10.0|^11.0", "illuminate/contracts": "^9.52.16|^10.0|^11.0", "illuminate/support": "^9.52.16|^10.0|^11.0", "php": "^8.0", "symfony/console": "^6.0|^7.0", "symfony/yaml": "^6.0|^7.0"}, "require-dev": {"orchestra/testbench": "^7.0|^8.0|^9.0", "phpstan/phpstan": "^1.10"}, "bin": ["bin/sail"], "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Sail\\SailServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Sail\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Docker files for running a basic Laravel application.", "keywords": ["docker", "laravel"], "support": {"issues": "https://github.com/laravel/sail/issues", "source": "https://github.com/laravel/sail"}, "time": "2024-03-20T20:09:31+00:00"}, {"name": "mockery/mockery", "version": "1.6.11", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "81a161d0b135df89951abd52296adf97deb0723d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/81a161d0b135df89951abd52296adf97deb0723d", "reference": "81a161d0b135df89951abd52296adf97deb0723d", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2024-03-21T18:34:15+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2023-03-08T13:26:56+00:00"}, {"name": "nunomaduro/collision", "version": "v7.10.0", "source": {"type": "git", "url": "https://github.com/nunomaduro/collision.git", "reference": "49ec67fa7b002712da8526678abd651c09f375b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/collision/zipball/49ec67fa7b002712da8526678abd651c09f375b2", "reference": "49ec67fa7b002712da8526678abd651c09f375b2", "shasum": ""}, "require": {"filp/whoops": "^2.15.3", "nunomaduro/termwind": "^1.15.1", "php": "^8.1.0", "symfony/console": "^6.3.4"}, "conflict": {"laravel/framework": ">=11.0.0"}, "require-dev": {"brianium/paratest": "^7.3.0", "laravel/framework": "^10.28.0", "laravel/pint": "^1.13.3", "laravel/sail": "^1.25.0", "laravel/sanctum": "^3.3.1", "laravel/tinker": "^2.8.2", "nunomaduro/larastan": "^2.6.4", "orchestra/testbench-core": "^8.13.0", "pestphp/pest": "^2.23.2", "phpunit/phpunit": "^10.4.1", "sebastian/environment": "^6.0.1", "spatie/laravel-ignition": "^2.3.1"}, "type": "library", "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "autoload": {"files": ["./src/Adapters/Phpunit/Autoload.php"], "psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["artisan", "cli", "command-line", "console", "error", "handling", "laravel", "laravel-zero", "php", "symfony"], "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2023-10-11T15:45:01+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpunit/php-code-coverage", "version": "10.1.14", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "e3f51450ebffe8e0efdf7346ae966a656f7d5e5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/e3f51450ebffe8e0efdf7346ae966a656f7d5e5b", "reference": "e3f51450ebffe8e0efdf7346ae966a656f7d5e5b", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1", "phpunit/php-file-iterator": "^4.0", "phpunit/php-text-template": "^3.0", "sebastian/code-unit-reverse-lookup": "^3.0", "sebastian/complexity": "^3.0", "sebastian/environment": "^6.0", "sebastian/lines-of-code": "^2.0", "sebastian/version": "^4.0", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "10.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.14"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-12T15:33:41+00:00"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/a95037b6d9e608ba092da1b23931e537cadc3c3c", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T06:24:48+00:00"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:56:09+00:00"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/0c7b06ff49e3d5072f057eb1fa59258bf287a748", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T14:07:24+00:00"}, {"name": "phpunit/php-timer", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/e2a2d67966e740530f4a3343fe2e030ffdc1161d", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:57:52+00:00"}, {"name": "phpunit/phpunit", "version": "10.5.20", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "547d314dc24ec1e177720d45c6263fb226cc2ae3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/547d314dc24ec1e177720d45c6263fb226cc2ae3", "reference": "547d314dc24ec1e177720d45c6263fb226cc2ae3", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.1", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.5", "phpunit/php-file-iterator": "^4.0", "phpunit/php-invoker": "^4.0", "phpunit/php-text-template": "^3.0", "phpunit/php-timer": "^6.0", "sebastian/cli-parser": "^2.0", "sebastian/code-unit": "^2.0", "sebastian/comparator": "^5.0", "sebastian/diff": "^5.0", "sebastian/environment": "^6.0", "sebastian/exporter": "^5.1", "sebastian/global-state": "^6.0.1", "sebastian/object-enumerator": "^5.0", "sebastian/recursion-context": "^5.0", "sebastian/type": "^4.0", "sebastian/version": "^4.0"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/10.5.20"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2024-04-24T06:32:35+00:00"}, {"name": "sebastian/cli-parser", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/c34583b87e7b7a8055bf6c450c2c77ce32a24084", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:12:49+00:00"}, {"name": "sebastian/code-unit", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/a81fee9eef0b7a76af11d121767abc44c104e503", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:58:43+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:59:15+00:00"}, {"name": "sebastian/comparator", "version": "5.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2db5010a484d53ebf536087a70b4a5423c102372"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2db5010a484d53ebf536087a70b4a5423c102372", "reference": "2db5010a484d53ebf536087a70b4a5423c102372", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-14T13:18:12+00:00"}, {"name": "sebastian/complexity", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "68ff824baeae169ec9f2137158ee529584553799"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/68ff824baeae169ec9f2137158ee529584553799", "reference": "68ff824baeae169ec9f2137158ee529584553799", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/3.2.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:37:17+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/c41e007b4b62af48218231d6c2275e4c9b975b2e", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:15:17+00:00"}, {"name": "sebastian/environment", "version": "6.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/8074dbcd93529b357029f5cc5058fd3e43666984", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-23T08:47:14+00:00"}, {"name": "sebastian/exporter", "version": "5.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "955288482d97c19a372d3f31006ab3f37da47adf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/955288482d97c19a372d3f31006ab3f37da47adf", "reference": "955288482d97c19a372d3f31006ab3f37da47adf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/5.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:17:12+00:00"}, {"name": "sebastian/global-state", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:19:19+00:00"}, {"name": "sebastian/lines-of-code", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/856e7f6a75a84e339195d48c556f23be2ebf75d0", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:38:20+00:00"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/202d0e344a580d7f7d04b3fafce6933e59dae906", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:08:32+00:00"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/24ed13d98130f0e7122df55d06c5c4942a577957", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:06:18+00:00"}, {"name": "sebastian/recursion-context", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "05909fb5bc7df4c52992396d0116aed689f93712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/05909fb5bc7df4c52992396d0116aed689f93712", "reference": "05909fb5bc7df4c52992396d0116aed689f93712", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:05:40+00:00"}, {"name": "sebastian/type", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/462699a16464c3944eefc02ebdd77882bd3925bf", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:10:45+00:00"}, {"name": "sebastian/version", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c51fa83a5d8f43f1402e3f32a005e6262244ef17", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-07T11:34:05+00:00"}, {"name": "spatie/backtrace", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/spatie/backtrace.git", "reference": "8373b9d51638292e3bfd736a9c19a654111b4a23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/backtrace/zipball/8373b9d51638292e3bfd736a9c19a654111b4a23", "reference": "8373b9d51638292e3bfd736a9c19a654111b4a23", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"ext-json": "*", "laravel/serializable-closure": "^1.3", "phpunit/phpunit": "^9.3", "spatie/phpunit-snapshot-assertions": "^4.2", "symfony/var-dumper": "^5.1"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Backtrace\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "A better backtrace", "homepage": "https://github.com/spatie/backtrace", "keywords": ["Backtrace", "spatie"], "support": {"source": "https://github.com/spatie/backtrace/tree/1.6.1"}, "funding": [{"url": "https://github.com/sponsors/spatie", "type": "github"}, {"url": "https://spatie.be/open-source/support-us", "type": "other"}], "time": "2024-04-24T13:22:11+00:00"}, {"name": "spatie/flare-client-php", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/spatie/flare-client-php.git", "reference": "e27977d534eefe04c154c6fd8460217024054c05"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/flare-client-php/zipball/e27977d534eefe04c154c6fd8460217024054c05", "reference": "e27977d534eefe04c154c6fd8460217024054c05", "shasum": ""}, "require": {"illuminate/pipeline": "^8.0|^9.0|^10.0|^11.0", "php": "^8.0", "spatie/backtrace": "^1.5.2", "symfony/http-foundation": "^5.2|^6.0|^7.0", "symfony/mime": "^5.2|^6.0|^7.0", "symfony/process": "^5.2|^6.0|^7.0", "symfony/var-dumper": "^5.2|^6.0|^7.0"}, "require-dev": {"dms/phpunit-arraysubset-asserts": "^0.5.0", "pestphp/pest": "^1.20|^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "spatie/phpunit-snapshot-assertions": "^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.3.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\FlareClient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Send PHP errors to <PERSON><PERSON><PERSON>", "homepage": "https://github.com/spatie/flare-client-php", "keywords": ["exception", "flare", "reporting", "spatie"], "support": {"issues": "https://github.com/spatie/flare-client-php/issues", "source": "https://github.com/spatie/flare-client-php/tree/1.5.1"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2024-05-03T15:43:14+00:00"}, {"name": "spatie/ignition", "version": "1.14.1", "source": {"type": "git", "url": "https://github.com/spatie/ignition.git", "reference": "c23cc018c5f423d2f413b99f84655fceb6549811"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/ignition/zipball/c23cc018c5f423d2f413b99f84655fceb6549811", "reference": "c23cc018c5f423d2f413b99f84655fceb6549811", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^8.0", "spatie/backtrace": "^1.5.3", "spatie/flare-client-php": "^1.4.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "require-dev": {"illuminate/cache": "^9.52|^10.0|^11.0", "mockery/mockery": "^1.4", "pestphp/pest": "^1.20|^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "psr/simple-cache-implementation": "*", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "vlucas/phpdotenv": "^5.5"}, "suggest": {"openai-php/client": "Require get solutions from OpenAI", "simple-cache-implementation": "To cache solutions from OpenAI"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.5.x-dev"}}, "autoload": {"psr-4": {"Spatie\\Ignition\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A beautiful error page for PHP applications.", "homepage": "https://flareapp.io/ignition", "keywords": ["error", "flare", "laravel", "page"], "support": {"docs": "https://flareapp.io/docs/ignition-for-laravel/introduction", "forum": "https://twitter.com/flareappio", "issues": "https://github.com/spatie/ignition/issues", "source": "https://github.com/spatie/ignition"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2024-05-03T15:56:16+00:00"}, {"name": "spatie/laravel-ignition", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/spatie/laravel-ignition.git", "reference": "f52124d50122611e8a40f628cef5c19ff6cc5b57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-ignition/zipball/f52124d50122611e8a40f628cef5c19ff6cc5b57", "reference": "f52124d50122611e8a40f628cef5c19ff6cc5b57", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "illuminate/support": "^10.0|^11.0", "php": "^8.1", "spatie/flare-client-php": "^1.5", "spatie/ignition": "^1.14", "symfony/console": "^6.2.3|^7.0", "symfony/var-dumper": "^6.2.3|^7.0"}, "require-dev": {"livewire/livewire": "^2.11|^3.3.5", "mockery/mockery": "^1.5.1", "openai-php/client": "^0.8.1", "orchestra/testbench": "8.22.3|^9.0", "pestphp/pest": "^2.34", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan-deprecation-rules": "^1.1.1", "phpstan/phpstan-phpunit": "^1.3.16", "vlucas/phpdotenv": "^5.5"}, "suggest": {"openai-php/client": "Require get solutions from OpenAI", "psr/simple-cache-implementation": "Needed to cache solutions from OpenAI"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\LaravelIgnition\\IgnitionServiceProvider"], "aliases": {"Flare": "Spatie\\LaravelIgnition\\Facades\\Flare"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\LaravelIgnition\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A beautiful error page for Laravel applications.", "homepage": "https://flareapp.io/ignition", "keywords": ["error", "flare", "laravel", "page"], "support": {"docs": "https://flareapp.io/docs/ignition-for-laravel/introduction", "forum": "https://twitter.com/flareappio", "issues": "https://github.com/spatie/laravel-ignition/issues", "source": "https://github.com/spatie/laravel-ignition"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2024-05-02T13:42:49+00:00"}, {"name": "symfony/yaml", "version": "v6.4.7", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "53e8b1ef30a65f78eac60fddc5ee7ebbbdb1dee0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/53e8b1ef30a65f78eac60fddc5ee7ebbbdb1dee0", "reference": "53e8b1ef30a65f78eac60fddc5ee7ebbbdb1dee0", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-28T10:28:08+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^8.1"}, "platform-dev": [], "plugin-api-version": "2.6.0"}