<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use TCG\Voyager\Http\Controllers\VoyagerBaseController;
use App\Models\Contact;

class ContactController extends VoyagerBaseController
{
    public function show(Request $request, $id)
    {
        // Mark as read when viewing
        $contact = Contact::findOrFail($id);
        if (!$contact->is_read) {
            $contact->update(['is_read' => true]);
        }

        return parent::show($request, $id);
    }

    public function edit(Request $request, $id)
    {
        // Mark as read when editing
        $contact = Contact::findOrFail($id);
        if (!$contact->is_read) {
            $contact->update(['is_read' => true]);
        }

        return parent::edit($request, $id);
    }
}
