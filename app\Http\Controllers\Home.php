<?php

namespace App\Http\Controllers;

use RealRashid\SweetAlert\Facades\Alert;
use GuzzleHttp\Client;
use League\ISO3166\ISO3166;
use App\Models\Newsletter;
use App\Models\CoursesCategory;
use App\Models\Team;
use App\Models\About;
use App\Models\Course;
use TCG\Voyager\Models\Post;
use App\Models\Testimonial;
use App\Models\Pricing;
use App\Models\Contact;
use App\Models\Comment;
use App\Models\CourseReview;
use App\Models\TrialClass;
use TCG\Voyager\Models\Category;
use TCG\Voyager\Facades\Voyager;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Mail;
use App\Mail\TrialClassRegistered;
use App\Mail\UserTrialClassConfirmation;
use Illuminate\Http\Request;

class Home extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $testimonials = Testimonial::approved()->get();
        $teams = Team::all();
        $categories = CoursesCategory::withCount('courses')->get();
        $courses = Course::latest()->paginate(6);
        $posts = Post::latest()->paginate(3);
        return view('index', compact('courses', 'categories', 'teams', 'testimonials','posts'));
    }

    /*************** Newsletter ******************/

    public function storeNewsletter(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $existingEmail = Newsletter::where('email', $request->email)->exists();
        if ($existingEmail) {
            Alert::error('Error', 'You are already registered.');
            return redirect()->back();
        }
        Newsletter::create([
            'email' => $request->email,
        ]);
        Alert::success('Success', 'Thank you for subscribing!');
        return redirect()->route('home');
    }

    /*****************Courses ************************/

    public function coursesPage()
    {
        $courses = Course::with('category')->get();
        $categories = CoursesCategory::withCount('courses')->get();
        return view('courses', compact('courses', 'categories'));
    }
    public function showAmp($id)
    {
        $post = Post::findOrFail($id);
        return view('post-amp', compact('post'));
    }

    public function filterByCategorycourse($slug)
    {
        $category = CoursesCategory::where('slug', $slug)->firstOrFail();
        $courses = Course::where('category_id', $category->id)->paginate(6);
        $categories = CoursesCategory::withCount('courses')->get();
        return view('category-courses', compact('courses', 'categories', 'category'));
    }

    public function courseShow($slug)
    {
        $categories = CoursesCategory::withCount('courses')->get();
        $course = Course::where('slug', $slug)->firstOrFail();
        $recentCourses = Course::where('slug', '!=', $slug)->orderBy('created_at', 'desc')->take(4)->get();

        // Get approved reviews for this course
        $reviews = $course->approvedReviews()->latest()->get();

        return view('course-view', compact('course', 'categories','recentCourses', 'reviews'));
    }

    /********************Team ******************/
    public function teamPage()
    {
        $teams = Team::all();
        return view('team', compact('teams'));
    }

    public function aboutPage()
    {
        $testimonials = Testimonial::all();
        $teams = Team::all();
        // $abouts = About::all();
        return view('about', compact('testimonials', 'teams'));
    }


    /****************** policies ************/
    public function policiespage()
    {
        return view('policies');
    }

    /**************** Trial Class *************/

    public function submitTrial(Request $request)
    {
    $validatedData = $request->validate([
        'first_name' => 'required|string|max:255',
        'last_name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'required|string|max:255',
        'country' => 'required|string|max:255',
        'choose_course' => 'required|string|max:255',
        'state' => 'required|string|max:255',
        'notes' => 'nullable|string',
        // 'is_read' => 'required',
    ]);

    $existingRecord = TrialClass::where([
        'first_name' => $validatedData['first_name'],
        'last_name' => $validatedData['last_name'],
        'email' => $validatedData['email'],
        'phone' => $validatedData['phone'],
        'state' => $validatedData['state'],
        'country' => $validatedData['country'],
        'choose_course' => $validatedData['choose_course'],
        'notes' => $validatedData['notes'],
        // 'is_read' => $validatedData['is_read'],
    ])->exists();

    if ($existingRecord) {
        Alert::error('Error', 'This trial class information has already been submitted.');
        return redirect(route('home'));
    }

    $validatedData['is_read'] = false;
    $validatedData['is_approved'] = false;
    TrialClass::create($validatedData);

    $adminEmail = setting('site.email');
    Mail::to($adminEmail)->send(new TrialClassRegistered($validatedData));

    Mail::to($validatedData['email'])->send(new UserTrialClassConfirmation($validatedData));

    Alert::success('Success', 'Trial class information submitted successfully. Please check your email for confirmation.');
    return redirect(route('home'));
    }

    public function getUnreadCount()
    {
        $unreadCount = TrialClass::where('is_read', false)->count();
        return response()->json(['unread_count' => $unreadCount]);
    }

    public function trialClassPage()
    {
        $iso3166 = new ISO3166();
        $countries = $iso3166->all();

        foreach ($countries as &$country) {
            $countryCode = strtolower($country['alpha2']);

            $flagUrl = "https://flagcdn.com/64x48/{$countryCode}.png";

            $country['flag'] = $flagUrl;
        }

        usort($countries, function ($a, $b) {
            return strcmp($a['name'], $b['name']);
        });

        return view('trial-class', compact('countries'));
    }

    /********** Pricing **************/

    public function pricingPage()
    {
        $plans = Pricing::all();
        return view('pricing', compact('plans'));
    }

    /***************** Contact **********/

    public function storeContact(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);
        $existingContact = Contact::where([
            'name' => $validatedData['name'],
            'phone' => $validatedData['phone'],
            'email' => $validatedData['email'],
            'subject' => $validatedData['subject'],
            'message' => $validatedData['message'],
        ])->exists();

        if ($existingContact) {
            Alert::error('error', 'You have already sent this message before.');
            return redirect(route('home'));
        }

        $validatedData['is_approved'] = false;
        $validatedData['is_read'] = false;
        Contact::create($validatedData);
        Alert::success('Success', 'Message sent successfully');
        return redirect(route('home'));
    }

    /************ Testemonials *******/

    public function getTestimonial()
    {
        $testimonials = Testimonial::approved()->get();
        return view('testimonial', compact('testimonials'));
    }

    public function storeTestimonial(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'comment' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('testimonials', 'public');
        }

        // Create new testimonial (pending approval)
        Testimonial::create([
            'name' => $request->name,
            'country' => $request->country,
            'comment' => $request->comment,
            'image' => $imagePath ?? null,
            'is_approved' => false,
            'is_read' => false,
        ]);
        Alert::success('success', 'Thank you for your testimonial!');
        return redirect()->back();
    }
    /********** Blog *******/

    public function blogPage()
    {
        $posts = Post::latest()->paginate(10);
        $recentPosts = Post::latest()->take(5)->get();
        $categories = Category::withCount('posts')->get();
        return view('blog', compact('posts', 'categories','recentPosts'));
    }

    public function postShow($slug)
    {
        $post = Voyager::model('Post')->where('slug', $slug)->firstOrFail();
        $post->increment('views');
        $recentPosts = Post::latest()->take(5)->get();
        $categories = Category::withCount('posts')->get();
        $comments = $post->comments()->latest()->get();

        return view('blog-view', compact('post', 'recentPosts', 'categories', 'comments'));
    }

    public function storeComment(Request $request)
    {
    $request->validate([
        'post_id' => 'required|exists:posts,id',
        'author_name' => 'required|string|max:255',
        'author_email' => 'required|email|max:255',
        'avatar' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        'subject' => 'required|string|max:255',
        'body' => 'required|string',
    ]);

    $comment = new Comment();

    if ($request->hasFile('avatar')) {
        $avatarPath = $request->file('avatar')->store('avatars', 'public');
        $comment->avatar = $avatarPath;
    }

    $comment->fill($request->all());
    $comment->save();

    Alert::success('success', 'Comment added successfully');
    return redirect()->back();
    }


    public function filterByCategory($slug)
    {
        $category = Category::where('slug', $slug)->firstOrFail();
        $recentPosts = Post::latest()->take(5)->get();
        $categories = Category::withCount('posts')->get();
        $posts = Post::where('category_id', $category->id)->paginate(10);

        return view('category-posts', compact('posts', 'category','recentPosts', 'categories'));
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /*************** Course Reviews ******************/

    public function storeCourseReview(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'user_name' => 'required|string|max:255',
            'user_email' => 'required|email|max:255',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'required|string|max:1000',
        ]);

        // Check if user already reviewed this course
        $existingReview = CourseReview::where('course_id', $request->course_id)
            ->where('user_email', $request->user_email)
            ->first();

        if ($existingReview) {
            Alert::error('Error', 'You have already reviewed this course.');
            return redirect()->back();
        }

        CourseReview::create([
            'course_id' => $request->course_id,
            'user_name' => $request->user_name,
            'user_email' => $request->user_email,
            'rating' => $request->rating,
            'comment' => $request->comment,
            'is_approved' => false, // Reviews need approval by default
        ]);

        Alert::success('Success', 'Thank you for your review! It will be published after approval.');
        return redirect()->back();
    }
}
