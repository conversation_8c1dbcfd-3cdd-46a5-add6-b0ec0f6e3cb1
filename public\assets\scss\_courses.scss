/*======================================
    Courses CSS
========================================*/
.courses {
    background-color: $white;

    .section-title {
        margin-bottom: 40px;
    }

    &.grid-page {
        padding-top: 90px;
    }

    &.style2 {
        background: $white;

        .single-course {
            background: $white;

            .content {
                position: relative;

                .price {
                    height: 65px;
                    width: 65px;
                    line-height: 65px;
                    text-align: center;
                    font-weight: 700;
                    font-size: 16px;
                    position: absolute;
                    right: 20px;
                    top: -30px;
                    background: $theme-color;
                    color: $white;
                    border-radius: 50%;
                }

                .date {
                    font-size: 12px;
                    display: block;
                    margin-bottom: 14px;
                    font-weight: 500;
                    display: inline-block;
                    border: 1px solid #eee;
                    padding: 6px 18px;
                    border-radius: 30px;
                }

                h3 {
                    line-height: 32px;
                }
            }
        }

        .button {
            text-align: center;
            margin-top: 70px;
        }
    }

    .single-course {
        margin-top: 30px;
        transition: all 0.4s ease-in-out;

        .course-image {
            position: relative;
            overflow: hidden;

            img {
                width: 100%;
                transition: all 0.4s ease;
            }

            .price {
                color: $white;
                background-color: $theme-color;
                font-size: 14px;
                font-weight: 600;
                position: absolute;
                right: 0;
                top: 25px;
                padding: 0;
                padding-right: 0px;
                padding-left: 0px;
                height: 36px;
                line-height: 36px;
                padding-right: 20px;
                padding-left: 5px;

                &::before {
                    position: absolute;
                    content: "";
                    left: -32px;
                    top: 0;
                    border: 18px solid $theme-color;
                    border-left-color: transparent;
                    height: 10px;
                    width: 10px !important;
                    border-left-color: transparent;
                }
            }
        }

        &:hover {
            box-shadow: 0px 0px 20px #00000012;

            .course-image {
                img {
                    transform: scale(1.1);
                }
            }
        }

        .content {
            padding: 30px;
            padding-top: 25px;
            border: 1px solid #eee;
            border-top: none;


            h3 {
                display: block;
                margin-bottom: 15px;

                a {
                    font-size: 20px;
                    font-weight: 700;
                    color: $black;

                    &:hover {
                        color: $theme-color;
                    }
                }
            }

        }

        .bottom-content {
            padding: 20px 30px;
            border: 1px solid #eee;
            border-top: none;
            overflow: hidden;

            .review {
                float: left;

                li {
                    display: inline-block;

                    i {
                        color: #FFAA30;
                    }

                    &:last-child {
                        color: $black;
                        font-weight: 500;
                        font-size: 13px;
                        margin-left: 5px;
                    }
                }
            }

            .tag {
                float: right;

                i {
                    color: $theme-color;
                    display: inline-block;
                    margin-right: 4px;
                    font-size: 14px;
                }

                a {

                    font-size: 13px;
                    font-weight: 500;
                    color: $black;

                    &:hover {
                        color: $theme-color;
                    }
                }

            }
        }

    }

}

/*======================================
    Course Details CSS
========================================*/
.course-details {

    .bottom-content {
        overflow: hidden;
        padding: 20px 30px 20px 20px;
        border: 1px solid #eee;
        margin-top: 30px;

        .button {
            float: left;
        }

        .share {
            float: right;

            li {
                display: inline-block;
                margin-right: 15px;

                &:last-child {
                    margin: 0;
                }

                span {
                    font-size: 13px;
                    font-weight: 500;
                    color: $black;
                    text-transform: capitalize;
                }

                a {
                    font-size: 15px;
                    color: $black;

                    &:hover {
                        color: $theme-color;
                    }
                }
            }
        }
    }

    .nav-tabs {
        border: none;
        background: $gray;
        margin-bottom: 50px;

        li {
            margin-right: 5px;

            &:last-child {
                margin: 0;
            }

            button {
                padding: 15px 30px;
                border: none;
                background: transparent;
                background-color: transparent;
                font-size: 14px;
                font-weight: 600;
                border: none;
                border-radius: 0;
                position: relative;

                &::before {
                    position: absolute;
                    content: "";
                    left: 50%;
                    margin-left: -10px;
                    bottom: -14px;
                    border: 10px solid $theme-color;
                    border-bottom-color: transparent;
                    border-right-color: transparent;
                    border-left-color: transparent;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.4s ease;
                }

                &.active:before {
                    bottom: -19px;
                    opacity: 1;
                    visibility: visible;
                }

                &.active {
                    color: $white;
                    background: $theme-color;
                }

                &:hover {
                    color: $white;
                    background: $theme-color;
                }
            }
        }
    }

    /* Course Overview */
    .course-overview {
        .title {
            font-size: 25px;
            line-height: 1.3;
            font-weight: 700;
            margin-bottom: 18px;
        }

        p {
            margin-bottom: 25px;

            &:last-child {
                margin: 0;
            }
        }

        .overview-course-video {
            margin: 45px 0 42px;
            border-radius: 5px;

            iframe {
                width: 100%;
                height: 435px;
                border: 0;
            }
        }


    }

    /* Course Curriculum */
    .course-curriculum {
        .single-curriculum-section {
            border: 1px solid #dedede;
            border-radius: 0;
            overflow: hidden;
            margin-top: 50px;

            .course-item:nth-child(2n+1) {
                background-color: #f8f8f8;
            }

            ul.section-content .course-item .section-item-link {
                padding: 0 30px 0 48px;
                min-height: 56px;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;

                &:hover {
                    .item-name {
                        color: $theme-color;
                    }

                    .course-item-meta {
                        i {
                            color: $theme-color;
                        }
                    }
                }

                .item-name {
                    -webkit-box-flex: 1;
                    -ms-flex-positive: 1;
                    flex-grow: 1;
                    padding: 10px 10px 10px 0;
                    max-width: 235px;
                    color: $black;
                    font-size: 13px;
                    font-weight: 500;
                }

                .course-item-meta {
                    display: table-cell;
                    vertical-align: middle;
                    white-space: nowrap;
                    padding: 10px 0;
                    text-align: right;

                    i {
                        font-size: 15px;
                        color: #666;
                        transition: all 0.4s ease;
                    }

                    .item-meta {
                        text-align: center;
                        display: inline-block;
                        vertical-align: middle;
                        height: 28px;
                        line-height: 28px;
                        border: 1px solid transparent;
                        border-radius: 0;
                        margin-left: 10px;
                        font-size: 12px;
                        font-weight: 500;
                        padding: 0 15px;

                        &.duration {
                            color: #de7e5b;
                            background: #f4ebe7;
                        }

                        &.item-meta-icon {
                            height: 28px;
                            line-height: 28px;
                            margin-left: 10px;
                            font-size: 12px;
                            font-weight: 500;
                        }

                        &.count-questions {
                            color: #2dbbc4;
                            background: #e3f1f2;
                        }
                    }
                }
            }
        }

        .section-left {
            padding: 22px 48px;

            .section-desc {
                font-size: 13px;
                margin-top: 5px;
                color: #777;
            }

            .title {
                font-size: 20px;
                font-weight: 700;
                line-height: 1.3;
                margin-bottom: 0;
            }
        }

    }

    /* Course Instructor */
    .course-instructor {
        padding-bottom: 30px;

        .profile-image {
            img {
                width: 100%;
            }
        }

        .profile-info {
            h5 {
                a {
                    font-size: 18px;
                    font-weight: 600;

                    &:hover {
                        color: $theme-color;
                    }
                }
            }

            .author-career {
                display: block;
                margin-top: 5px;
            }

            .author-bio {
                margin-top: 30px;
            }
        }

        .author-social-networks {
            margin-top: 30px;

            li {
                display: inline-block;
                margin-right: 2px;

                &:last-child {
                    margin: 0;
                }

                a {
                    height: 40px;
                    width: 40px;
                    line-height: 40px;
                    text-align: center;
                    display: block;
                    border: 1px solid #eee;
                    color: #777;
                    border-radius: 50%;
                    font-size: 13px;

                    &:hover {
                        color: $white;
                        background: $theme-color;
                        border-color: transparent;
                    }
                }
            }
        }
    }

    /* Course Reviews */
    .course-reviews {
        .title {
            font-size: 28px;
            line-height: 1.3;
            font-weight: 600;
            margin-bottom: 18px;
        }

        .post-comments {
            margin: 0;
            margin-top: 30px;
        }

        .comments-list {
            li {
                position: relative;

                .rating-star {
                    display: block;
                    margin-top: 5px;

                    li {
                        display: inline-block;
                        margin: 0;
                        padding: 0;
                        border: none;

                        i {
                            color: #F6B500;
                        }
                    }
                }

                .name {
                    a {
                        font-size: 17px;
                        font-weight: 600;
                        color: $black;

                        &:hover {
                            color: $theme-color;
                        }
                    }
                }

                .time {
                    position: absolute;
                    right: 0;
                    top: 0;
                    font-size: 13px;
                    font-weight: 500;
                    color: #919191;
                    border-radius: 30px;
                }
            }
        }
    }

    /* Course Sidebar */
    .course-sidebar {
        .sidebar-widget {
            margin-bottom: 40px;
            padding: 40px;
            border: 1px solid #eee;

            &:last-child {
                margin: 0;
            }

            &.other-course-wedget {
                padding-bottom: 15px;
            }

            .sidebar-widget-course {
                .single-course {
                    margin-bottom: 30px;
                    padding-bottom: 30px;
                    border-bottom: 1px solid #eee;
                    overflow: hidden;
                    position: relative;
                    min-height: 130px;

                    &:last-child {
                        margin: 0;
                        padding: 0;
                        border: none;
                    }

                    .thumbnail {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 100px;
                        height: 100px;
                        border-radius: 50%;
                        overflow: hidden;

                        img {
                            width: 100px;
                            height: 100px;
                            border-radius: 50%;
                            transition: all 0.4s ease;
                        }

                        &:hover {
                            img {
                                transform: scale(1.1);
                            }
                        }
                    }

                    .info {
                        display: inline-block;
                        padding: 0;
                        padding-left: 120px;

                        .price {
                            font-size: 15px;
                            font-weight: 600;
                            color: $theme-color;
                            display: block;
                            margin-bottom: 5px;
                        }

                        .title {
                            line-height: 20px;

                            a {
                                font-size: 15px;
                                font-weight: 500;
                                color: $black;

                                &:hover {
                                    color: $theme-color;
                                }
                            }
                        }
                    }
                }
            }

            .sidebar-widget-title {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 30px;
            }

            .sidebar-widget-search form {
                position: relative;

                input {
                    width: 100%;
                    min-height: 56px;
                    padding: 3px 30px;
                    padding-right: 80px;
                    color: #696969;
                    border: 1px solid #f5f5f5;
                    border-radius: 5px;
                    outline: 0;
                    background-color: #fff;
                    border-radius: 0;
                    border: 1px solid #eee;
                }

                button {
                    position: absolute;
                    top: 0;
                    right: 0;
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-align: center;
                    -ms-flex-align: center;
                    align-items: center;
                    -webkit-box-pack: center;
                    -ms-flex-pack: center;
                    justify-content: center;
                    width: 56px;
                    height: 56px;
                    color: $white;
                    background: $theme-color;
                    border: none;
                    border-radius: 4px;
                    transition: all 0.4s ease;
                    border-radius: 0;

                    &:hover {
                        color: $white;
                        background: $black;
                    }
                }
            }

        }
    }

}