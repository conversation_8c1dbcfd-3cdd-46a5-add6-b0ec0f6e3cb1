<?php $__env->startSection('seo'); ?>
    <!-- Meta Tags for SEO -->
    <title><?php echo e(setting('site.title')); ?></title>
    <meta name="description" content="<?php echo e(setting('site.description')); ?>">
    <meta name="keywords" content="<?php echo e(setting('site.keywords')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .course-description {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }

    .course-actions {
        text-align: center;
        border-top: 1px solid #eee;
        padding-top: 15px;
    }

    .add-review-btn {
        border: 1px solid #F6B500;
        color: #F6B500;
        padding: 8px 16px;
        border-radius: 5px;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .add-review-btn:hover {
        background: #F6B500;
        color: white;
        border-color: #F6B500;
    }

    .add-review-btn i {
        margin-right: 5px;
    }

    /* Modal Styles */
    .modal-content {
        border-radius: 12px;
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .modal-header {
        background: linear-gradient(135deg, #F6B500 0%, #e6a500 100%);
        color: white;
        border-radius: 12px 12px 0 0;
        border-bottom: none;
    }

    .modal-title {
        font-weight: 600;
    }

    .btn-close {
        filter: brightness(0) invert(1);
    }

    .course-info h6 {
        color: #333;
        font-weight: 600;
        font-size: 16px;
    }

    #modal_star_rating {
        display: flex;
        justify-content: center;
        gap: 5px;
        margin-top: 8px;
    }

    #modal_star_rating .star {
        font-size: 24px;
        color: #ddd;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    #modal_star_rating .star:hover,
    #modal_star_rating .star.hover {
        color: #F6B500;
        transform: scale(1.1);
    }

    #modal_star_rating .star.active {
        color: #F6B500;
    }

    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 10px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #F6B500;
        box-shadow: 0 0 0 0.2rem rgba(246, 181, 0, 0.15);
    }

    .modal-footer .btn-primary {
        background: #F6B500;
        border-color: #F6B500;
        padding: 10px 25px;
        font-weight: 600;
    }

    .modal-footer .btn-primary:hover {
        background: #e6a500;
        border-color: #e6a500;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title">our Courses</h1>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text</p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="index.html">Home</a></li>
                    <li>Our Courses</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start Courses Area -->
<section class="courses section grid-page">
    <div class="container">
        <div class="row">
            <!-- Main Content Area -->
            <div class="col-lg-8 col-md-7 col-12">
                <div class="row">
                    <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($index % 2 == 0 && $index != 0): ?>
                            </div><div class="row">
                        <?php endif; ?>
                        <!-- Start Single Course -->
                        <div class="col-lg-6 col-md-6 col-12">
                            <div class="single-course wow fadeInUp" data-wow-delay=".2s">
                                <div class="course-image">
                                    <a href="<?php echo e(route('courseShow', Str::slug($course->title))); ?>">
                                        <img src="<?php echo e(asset('storage/'. $course->image)); ?>" alt="<?php echo e($course->title); ?>">
                                    </a>
                                    <p class="price">$<?php echo e($course->price); ?></p>
                                </div>
                                <div class="content">
                                    <h3><a href="<?php echo e(route('courseShow', Str::slug($course->title))); ?>"><?php echo e($course->title); ?></a></h3>
                                    <p class="course-description"><?php echo e($course->description); ?></p>
                                </div>
                                <div class="bottom-content">
                                    <ul class="review">
                                        <?php if($course->hasReviews()): ?>
                                            <?php echo $course->star_rating; ?>

                                            <li><?php echo e($course->reviews_count); ?> <?php echo e($course->reviews_count == 1 ? 'Review' : 'Reviews'); ?></li>
                                        <?php else: ?>
                                            <li><i class="lni lni-star"></i></li>
                                            <li><i class="lni lni-star"></i></li>
                                            <li><i class="lni lni-star"></i></li>
                                            <li><i class="lni lni-star"></i></li>
                                            <li><i class="lni lni-star"></i></li>
                                            <li>No Reviews Yet</li>
                                        <?php endif; ?>
                                    </ul>
                                    <span class="tag">
                                        <i class="lni lni-tag"></i>
                                        <a href="<?php echo e(route('category.filter.course', $course->category->slug)); ?>"><?php echo e($course->category->title); ?></a>
                                    </span>
                                </div>
                                <div class="course-actions mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary add-review-btn"
                                            data-course-id="<?php echo e($course->id); ?>"
                                            data-course-title="<?php echo e($course->title); ?>"
                                            data-bs-toggle="modal"
                                            data-bs-target="#reviewModal">
                                        <i class="lni lni-star"></i> Add Review
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- End Single Course -->
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <!-- Pagination -->
                
                <!--/ End Pagination -->
            </div>
            <!-- Sidebar Area -->
            <aside class="col-lg-4 col-md-5 mt-4 col-12">
                <div class="sidebar">
                    <!-- Categories Widget -->
                    <div class="widget mt-2 categories-widget">
                        <h5 class="widget-title">Categories</h5>
                        <ul class="custom">
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li>
                                    <a href="<?php echo e(route('category.filter.course', $category->slug)); ?>"><?php echo e($category->title); ?> <span><?php echo e($category->courses_count); ?></span></a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
            </aside>
            <!-- End Sidebar Area -->
        </div>
    </div>
</section>
<!-- End Courses Area -->

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewModalLabel">Add Your Review</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reviewForm" action="<?php echo e(route('course-reviews.store')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="course_id" id="modal_course_id">

                    <div class="course-info mb-4">
                        <h6 id="modal_course_title"></h6>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="modal_user_name">Your Name *</label>
                                <input type="text" name="user_name" id="modal_user_name" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="modal_user_email">Your Email *</label>
                                <input type="email" name="user_email" id="modal_user_email" class="form-control" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="modal_rating">Your Rating *</label>
                        <div class="rating-input">
                            <input type="hidden" name="rating" id="modal_rating" value="5">
                            <div class="star-rating" id="modal_star_rating">
                                <span class="star" data-rating="1">★</span>
                                <span class="star" data-rating="2">★</span>
                                <span class="star" data-rating="3">★</span>
                                <span class="star" data-rating="4">★</span>
                                <span class="star" data-rating="5">★</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="modal_comment">Your Review *</label>
                        <textarea name="comment" id="modal_comment" class="form-control" rows="4" required placeholder="Share your experience with this course..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="reviewForm" class="btn btn-primary">Submit Review</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle Add Review button clicks
    document.querySelectorAll('.add-review-btn').forEach(button => {
        button.addEventListener('click', function() {
            const courseId = this.getAttribute('data-course-id');
            const courseTitle = this.getAttribute('data-course-title');

            // Set course info in modal
            document.getElementById('modal_course_id').value = courseId;
            document.getElementById('modal_course_title').textContent = courseTitle;

            // Reset form
            document.getElementById('reviewForm').reset();
            document.getElementById('modal_rating').value = '5';

            // Reset stars
            const stars = document.querySelectorAll('#modal_star_rating .star');
            stars.forEach((star, index) => {
                if (index < 5) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        });
    });

    // Handle star rating in modal
    const modalStars = document.querySelectorAll('#modal_star_rating .star');
    const modalRatingInput = document.getElementById('modal_rating');

    modalStars.forEach(star => {
        star.addEventListener('click', function() {
            const rating = this.getAttribute('data-rating');
            modalRatingInput.value = rating;

            // Update visual state
            modalStars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });

        star.addEventListener('mouseover', function() {
            const rating = this.getAttribute('data-rating');
            modalStars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('hover');
                } else {
                    s.classList.remove('hover');
                }
            });
        });
    });

    document.getElementById('modal_star_rating').addEventListener('mouseleave', function() {
        modalStars.forEach(s => s.classList.remove('hover'));
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\Sabeel-Ul-Quraan\resources\views/courses.blade.php ENDPATH**/ ?>