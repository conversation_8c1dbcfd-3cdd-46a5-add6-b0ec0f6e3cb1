<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use TCG\Voyager\Facades\Voyager;


class Comment extends Model
{
    use HasFactory;
    protected $table = 'comment';
    protected $fillable = [
        'author_name',
        'author_email',
        'subject',
        'body',
        'post_id',
        'avatar'
    ];
    public function post()
    {
        return $this->belongsTo(Voyager::modelClass('Post'));
    }
}
