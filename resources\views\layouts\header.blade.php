<style>
    .nav-link .arrow-down {
    display: inline-block;
    margin-left: 10px;
    border: solid #333;
    border-width: 0 2px 2px 0;
    padding: 3px;
    transform: rotate(45deg);
    transition: transform 0.3s ease-in-out;
}

/* Styles for larger screens */
@media (min-width: 992px) {
    .navbar-area {
        position: fixed;
        top: 56px; /* Adjust this value to match the height of the toolbar */
        z-index: 99;
        background: #315B58;
        box-shadow: 0px 20px 50px 0px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease-out;
        background: #fff;
        padding: 0px 0;
    }

    body {
        padding-top: 100px; /* Adjust according to your toolbar height */
    }

    .toolbar-area {
        transition: opacity 0.5s ease, visibility 0.5s ease;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 9999;
        background-color: #fff;
    }
}
/* Styles for smaller screens */
@media (max-width: 991px) {
    .navbar-area {
        position: fixed;
        top: 85px; /* Adjust this value to match the height of the toolbar */
        z-index: 99;
        background: #315B58;
        box-shadow: 0px 20px 50px 0px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease-out;
        background: #fff;
        padding: 0px 0;
    }

    body {
        padding-top: 240px; /* Adjust according to your toolbar height */
    }

    .toolbar-area {
        transition: opacity 0.5s ease, visibility 0.5s ease;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 9999;
        background-color: #fff;
    }
}



</style>


<!-- Preloader -->
<div class="preloader">
    <div class="preloader-inner">
        <div class="preloader-icon">
            <span></span>
            <span></span>
        </div>
    </div>
</div>
<!-- /End Preloader -->

<!-- Start Header Area -->
<header class="header navbar-area">
    <!-- Toolbar Start -->
    <div class="toolbar-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 col-md-6 col-12">
                    <div class="toolbar-social">
                        <ul>
                            <li><span class="title">Follow Us On : </span></li>
                            <li><a href="{{ setting('site.facebook') }}"><i class="lni lni-facebook-original"></i></a></li>
                            <li><a href="{{ setting('site.twitter') }}"><i class="lni lni-twitter-original"></i></a></li>
                            <li><a href="{{ setting('site.youtube') }}"><i class="lni lni-youtube"></i></a></li>
                            <li><a href="{{ setting('site.linkedin') }}"><i class="lni lni-linkedin-original"></i></a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-12" style="margin-top: 5px;">
                    <div class="toolbar-login">
                        <div class="button">
                            <a href="mailto:{{ setting('site.email') }}" style="font-size: 12px; display: inline-block; margin-right: 10px;">
                                <i class="lni lni-envelope" style="margin-right: 5px;"></i>{{ setting('site.email') }}
                            </a>
                            <a href="tel:{{ setting('site.mobile') }}" style="font-size: 12px; display: inline-block; margin-right: 10px;">
                                <i class="lni lni-phone" style="margin-right: 5px;"></i>{{ setting('site.mobile') }}
                            </a>
                            @php
                                function formatPhoneNumber($phone) {
                                    // افترض أن الرقم في صيغة دولية: 201234567890
                                    if (preg_match('/^20(\d{3})(\d{3})(\d{4})$/', $phone, $matches)) {
                                        return '+20 ' . $matches[1] . ' ' . $matches[2] . ' ' . $matches[3];
                                    }
                                    return $phone; // إذا لم يكن التنسيق متطابقًا، اعرض الرقم كما هو
                                }
                            @endphp
                            <a href="https://wa.me/{{ setting('site.whatsapp') }}" target="_blank" style="font-size: 12px; display: inline-block;">
                                <i class="lni lni-whatsapp" style="margin-right: 5px;"></i>{{ formatPhoneNumber(setting('site.whatsapp')) }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Toolbar End -->
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-12">
            <div class="nav-inner">
                <nav class="navbar navbar-expand-lg">
                    <a class="navbar-brand" href="{{ route('home') }}">
                        <img src="{{ asset('storage/' . setting('site.logo')) }}" alt="Logo" >
                    </a>
                    <button class="navbar-toggler mobile-menu-btn" type="button" data-bs-toggle="collapse"
                        data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                        <span class="toggler-icon"></span>
                        <span class="toggler-icon"></span>
                        <span class="toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse sub-menu-bar" id="navbarSupportedContent">
                        <ul id="nav" class="navbar-nav ms-auto">
                            <li class="nav-item">
                            <a class="{{ request()->is('/') ? 'active' : '' }}" href="{{ route('home') }}">Home</a>
                            </li>
                            {{-- <li class="nav-item"><a class="{{ request()->is('courses') ? 'active' : '' }}" href="{{ route('courses') }}">Courses</a></li> --}}
                            <li class="nav-item">
                                <a class="nav-link page-scroll dd-menu {{ request()->is('courses') || request()->is('courses/quraan') ? 'active' : '' }}"
                                   href="{{ route('courses') }}"
                                   data-bs-toggle="collapse" data-bs-target="#submenu-1-2"
                                   aria-controls="submenu-1-2" aria-expanded="false"
                                   aria-label="Toggle navigation">
                                   Courses <i class="arrow-down"></i>
                                </a>
                                <ul class="sub-menu collapse" id="submenu-1-2">
                                    <li class="nav-item"><a class="{{ request()->is('http://sabeel-ul-quraan.test/courses/quraan') ? 'active' : '' }}" href="http://sabeel-ul-quraan.test/courses/quraan">Courses Grid</a></li>
                                    <li class="nav-item"><a class="{{ request()->is('http://sabeel-ul-quraan.test/courses/quraan') ? 'active' : '' }}" href="http://sabeel-ul-quraan.test/courses/quraan">Course Details</a></li>
                                </ul>
                            </li>

                            <li class="nav-item"><a class="{{ request()->is('blog') ? 'active' : '' }}" href="{{ route('blog') }}">Blog</a></li>
                            <li class="nav-item"><a class="{{ request()->is('team') ? 'active' : '' }}" href="{{ route('team') }}">Team</a></li>
                            <li class="nav-item"><a class="{{ request()->is('pricing') ? 'active' : '' }}" href="{{ route('pricing') }}">Pricing</a></li>
                            <li class="nav-item"><a class="{{ request()->is('about') ? 'active' : '' }}" href="{{ route('about') }}">About</a></li>
                            <li class="nav-item"><a class="{{ request()->is('contact') ? 'active' : '' }}" href="{{ route('contact') }}">Contact</a></li>
                        </ul>
                    </div> <!-- navbar collapse -->

                    <div class="col-md-2 col-sm-4  text-end">
                        <div class="button-wrapper" data-wow-delay=".9s">
                            <a href="{{ route('trial-class') }}" class="btn btn-custom">Get Trial Class</a>
                        </div>
                    </div>
                </nav> <!-- navbar -->
            </div>
            </div>
        </div> <!-- row -->
    </div> <!-- container -->
</header>
<!-- End Header Area -->
<script>
document.addEventListener('scroll', function() {
    const toolbar = document.querySelector('.toolbar-area');
    const navbar = document.querySelector('.navbar-area');

    if (window.innerWidth >= 992) { // Large screens
        if (window.scrollY > 50) {
            toolbar.style.opacity = '0';
            toolbar.style.visibility = 'hidden';
            navbar.style.top = '0'; // Navbar takes the place of the toolbar
        } else {
            toolbar.style.opacity = '1';
            toolbar.style.visibility = 'visible';
            navbar.style.top = '56px'; // Reset navbar to below the toolbar
        }
    } else { // Small screens
        if (window.scrollY > 50) {
            toolbar.style.opacity = '0';
            toolbar.style.visibility = 'hidden';
            navbar.style.top = '0'; // Navbar takes the place of the toolbar
        } else {
            toolbar.style.opacity = '1';
            toolbar.style.visibility = 'visible';
            navbar.style.top = '85px'; // Reset navbar to below the toolbar
        }
    }
});



</script>
