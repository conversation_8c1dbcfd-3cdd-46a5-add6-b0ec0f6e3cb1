@extends('layouts.app')

@section('seo')
    <!-- Meta Tags for SEO -->
    {{-- <title>{{ setting('site.title') }}</title> --}}
    <meta name="description" content="{{ setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">
@endsection

@section('styles')
<style>
    .course-description {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }
</style>
@endsection

@section('content')
<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title">{{$category->title}}</h1>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text</p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="{{ route('home') }}">Home</a></li>
                    <li>{{$category->title}}</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start Courses Area -->
<section class="courses section grid-page">
    <div class="container">
        <div class="row">
            <!-- Main Content Area -->
            <div class="col-lg-8 col-md-7 col-12">
                <div class="row">
                    @foreach ($courses as $index => $course)
                        {{-- @if($index % 2 == 0 && $index != 0)
                            </div><div class="row">
                        @endif --}}
                        <!-- Start Single Course -->
                        <div class="col-lg-6 col-md-6 col-12">
                            <div class="single-course wow fadeInUp" data-wow-delay=".2s">
                                <div class="course-image">
                                    <a href="{{ route('courseShow', Str::slug($course->title)) }}">
                                        <img src="{{ asset('storage/'. $course->image) }}" alt="{{ $course->title }}">
                                    </a>
                                    <p class="price">${{ $course->price }}</p>
                                </div>
                                <div class="content">
                                    <h3><a href="{{ route('courseShow', Str::slug($course->title)) }}">{{ $course->title }}</a></h3>
                                    <p class="course-description">{{ $course->description }}</p>
                                </div>
                                <div class="bottom-content">
                                    <ul class="review">
                                        <li><i class="lni lni-star-filled"></i></li>
                                        <li><i class="lni lni-star-filled"></i></li>
                                        <li><i class="lni lni-star-filled"></i></li>
                                        <li><i class="lni lni-star-filled"></i></li>
                                        <li><i class="lni lni-star-filled"></i></li>
                                        <li>{{ $course->reviews }} Reviews</li>
                                    </ul>
                                    <span class="tag">
                                        <i class="lni lni-tag"></i>
                                        <a>{{ $category->title }}</a>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <!-- End Single Course -->
                    @endforeach

                </div>
                <!-- Pagination -->
                {{-- <div class="pagination center">
                    <ul class="pagination-list">
                        {{ $courses->links() }}
                    </ul>
                </div> --}}
                <!--/ End Pagination -->
            </div>
            <!-- Sidebar Area -->
            <aside class="col-lg-4 col-md-5 mt-4 col-12">
                <div class="sidebar">
                    <!-- Categories Widget -->
                    <div class="widget mt-2 categories-widget">
                        <h5 class="widget-title">Categories</h5>
                        <ul class="custom">
                            @foreach($categories as $category)
                                    <li>
                                        <a href="{{ route('category.filter.course', $category->slug) }}">{{ $category->title }} <span>{{ $category->courses_count }}</span></a>
                                    </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </aside>
            <!-- End Sidebar Area -->
        </div>
    </div>
</section>
<!-- End Courses Area -->

@endsection

@section('scripts')
@endsection
