@extends('layouts.app')
@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ setting('site.title') }}</title>
    <meta name="description" content="{{ setting('site.description') }}">
    <meta name="keywords" content="{{ setting('site.keywords') }}">
@endsection

@section('styles')
@endsection

@section('content')

 <!-- Start Breadcrumbs -->
 <div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title">Contact Us</h1>
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has
                        been the industry's standard dummy text</p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="index.html">Home</a></li>
                    <li>Contact Us</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start Contact Area -->
<section id="contact-us" class="contact-us section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-12 col-12">
                <div class="form-main">
                    <h3 class="title"><span>Ready to Start?</span>
                        Let's Talk
                    </h3>
                    <form class="form" method="post" action="{{ route('contact') }}">
                        @csrf
                        <div class="row">
                            <div class="col-lg-6 col-12">
                                <div class="form-group">
                                    <label>Your Name</label>
                                    <input name="name" type="text" placeholder="" required="required">
                                </div>
                                @error('name')
                                <div class="alert-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-lg-6 col-12">
                                <div class="form-group">
                                    <label>Your Subject</label>
                                    <input name="subject" type="text" placeholder=""
                                        required="required">
                                </div>
                                @error('subject')
                                <div class="alert-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-lg-6 col-12">
                                <div class="form-group">
                                    <label>Your Email Address</label>
                                    <input name="email" type="email" placeholder="" required="required">
                                </div>
                                @error('email')
                                <div class="alert-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-lg-6 col-12">
                                <div class="form-group">
                                    <label>Your Phone Number</label>
                                    <input name="phone" type="text" placeholder="" required="required">
                                </div>
                                @error('phone')
                                <div class="alert-danger">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-12">
                                <div class="form-group message">
                                    <label>Your Message</label>
                                    <textarea name="message" placeholder="" required="required"></textarea>
                                @error('message')
                                <div class="alert-danger">{{ $message }}</div>
                                @enderror
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group button">
                                    <button type="submit" class="btn ">Submit Message</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-lg-4 col-12">
                <div class="contact-info">
                    <!-- Start Single Info -->
                    <div class="single-info">
                        <i class="lni lni-map-marker"></i>
                        <h4>Visit Our Office</h4>
                        <p class="no-margin-bottom">{{ setting('site.location') }}</p>
                    </div>
                    <!-- End Single Info -->
                    <!-- Start Single Info -->
                    <div class="single-info">
                        <i class="lni lni-phone"></i>
                        <h4>Let's Talk</h4>
                        <p class="no-margin-bottom"><a href="tel:{{ setting('site.mobile') }}">Phone: {{ setting('site.mobile') }}</a></p>
                    </div>
                    <!-- End Single Info -->
                    <!-- Start Single Info -->
                    <div class="single-info">
                        <i class="lni lni-envelope"></i>
                        <h4>E-mail Us</h4>
                        <p class="no-margin-bottom"><a href="mailto:{{ setting('site.email') }}">{{ setting('site.email') }}</a></p>
                    </div>
                    <!-- End Single Info -->
                </div>
            </div>
        </div>
    </div>
</section>
<!--/ End Contact Area -->

<!-- Start Google-map Area -->
<div class="map-section">
    <div class="mapouter">
        <div class="gmap_canvas"><iframe width="100%" height="500" id="gmap_canvas"
                src="https://maps.google.com/maps?q=New%20York&t=&z=13&ie=UTF8&iwloc=&output=embed" frameborder="0"
                scrolling="no" marginheight="0" marginwidth="0"></iframe>
        </div>
    </div>
</div>
<!-- End Google-map Area -->

@endsection

@section('scripts')
@endsection
