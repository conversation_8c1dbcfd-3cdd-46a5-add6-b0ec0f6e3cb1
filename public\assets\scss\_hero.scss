/*======================================
    Hero Area CSS
========================================*/
.hero-area {
    position: relative;
    background-color: $gray;
    overflow: hidden;

    &.style2 {
        background-image: url('https://via.placeholder.com/1920x800');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: left;

        .hero-text {
            margin-top: 240px !important;
            text-align: left;

            h5 {
                background: $theme-color-style2;
                color: $white;
            }

            h1 {
                font-size: 40px;
                line-height: 55px;
                color: $black;

                span {
                    font-weight: 400;
                    border-bottom: 2px solid $theme-color-style2;
                }
            }

            p {
                color: $black;
            }

            .button {
                .btn {
                    background: $theme-color;
                    color: $white;

                    &::before {
                        background: $black;
                    }


                    i {
                        display: inline-block;
                        margin-right: 5px;
                    }
                }
            }
        }
    }

    &.style3 {
        background: $white;
        position: relative;

        &::before {
            position: absolute;
            content: "";
            left: 0;
            bottom: 0;
            height: 100px;
            width: 100%;
            background-image: url('../images/hero/shape1.svg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 9;
        }

        .hero-inner {
            position: relative;
            z-index: 2;
            padding: 0;
            background: $black;
            height: 780px;

            .inner-content {
                z-index: 5;
            }

            .hero-text {
                margin-top: 240px !important;
                text-align: left;

                h5 {
                    background: $white;
                    color: $black;
                }

                h1 {
                    font-size: 38px !important;
                    color: $white;
                    line-height: 55px !important;
                }

                p {
                    color: $white;
                }
            }

            .hero-image {

                img {
                    position: relative;
                    bottom: -60px;
                }
            }
        }


    }

    .tns-nav {
        text-align: center;
        position: absolute;
        bottom: 60px;
        transform: translateX(-50%);
        width: 100%;
        left: 50%;
        z-index: 9;

        button {
            height: 10px;
            width: 10px;
            background-color: $white;
            border-radius: 30px;
            display: inline-block;
            border: none;
            margin: 0px 5px;
            transition: all 0.4s ease;

            &.tns-nav-active {
                width: 20px;
            }
        }
    }

    .hero-inner {
        height: auto;
        background-size: cover;
        background-position: center;
        position: relative;
        z-index: 3;
        padding-bottom: 150px;

        &.overlay::before {
            opacity: 0.9;
            background: $black;
        }
    }

    .hero-image {
        margin-top: 90px;

        img {
            width: 100%;
        }
    }

    .hero-text {
        float: none;
        text-align: center;
        margin-top: 150px !important;

        h5 {
            color: $white !important;
            font-size: 14px;
            font-weight: 500;
            display: block;
            margin-bottom: 15px;
            display: inline-block;
            padding: 12px 22px;
            background: $theme-color !important;
            text-transform: none;
            border-radius: 30px;
        }

        h1 {
            font-weight: 700;
            margin-bottom: 25px;
            color: $white;
            font-size: 40px !important;
            line-height: 50px !important;

            span {
                color: $theme-color;
            }
        }

        p {
            font-size: 14px;
            color: $white;
        }

        .btn {
            border: none;
            background: $white;
            color: $black;

            &:hover {
                color: $white;
            }

            i {
                display: inline-block;
                margin-left: 5px;
            }

            &.alt-btn {
                background: $theme-color;
                color: $white;

                &::before {
                    background: $white;
                }

                &:hover {
                    color: $black !important;
                }
            }
        }

        .video-button {
            position: relative;
            height: 70px;
            width: 70px;
            line-height: 70px;
            text-align: center;
            border-radius: 50%;
            display: inline-block;
            background-color: $white;
            color: $black;
            margin-left: 50px;

            &:hover {
                color: $white;
                background-color: $theme-color;
            }
        }

        .video-button:before {
            position: absolute;
            content: '';
            left: 0;
            top: 0;
            height: 100%;
            width: 100%;
            border: 1px solid #fff;
            border-radius: 50%;
            -webkit-animation: pulse-border-2 1.5s linear infinite;
            animation: pulse-border-2 1.5s linear infinite;
        }

        .video-button:after {
            position: absolute;
            content: '';
            left: 0;
            top: 0;
            height: 100%;
            width: 100%;
            border: 1px solid #fff;
            border-radius: 50%;
            -webkit-animation: pulse-border 1s linear infinite;
            animation: pulse-border 1s linear infinite;
        }

        @-webkit-keyframes pulse-border {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                opacity: 1;
            }

            100% {
                -webkit-transform: scale(1.3);
                transform: scale(1.3);
                opacity: 0;
            }
        }

        @keyframes pulse-border {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                opacity: 1;
            }

            100% {
                -webkit-transform: scale(1.3);
                transform: scale(1.3);
                opacity: 0;
            }
        }

        @-webkit-keyframes pulse-border-2 {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                opacity: 1;
            }

            100% {
                -webkit-transform: scale(1.5);
                transform: scale(1.5);
                opacity: 0;
            }
        }

        @keyframes pulse-border-2 {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                opacity: 1;
            }

            100% {
                -webkit-transform: scale(1.5);
                transform: scale(1.5);
                opacity: 0;
            }
        }
    }



}

.hero-area .hero-text .button {
    margin-top: 35px;
}

.hero-area .hero-text .button .btn {
    margin-right: 10px;
}

.hero-area .hero-text .button .btn:hover {
    color: #fff;
}

.hero-area .hero-text .button .btn:last-child {
    margin-right: 0px;
}