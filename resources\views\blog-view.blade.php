@extends('layouts.app')

@section('seo')
    <!-- Meta Tags for SEO -->
    <title>{{ $post->title }}</title>
    <meta name="description" content="{{ $post->meta_description }}">
    <meta name="keywords" content="{{ $post->meta_keywords }}">
@endsection

@section('styles')
@endsection

@section('content')
<!-- Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <h1 class="page-title">{{ $post->title }}</h1>
                    <p>{{ $post->excerpt }}</p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="{{ route('home') }}">Home</a></li>
                    <li>{{ $post->title }}</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start Blog Single Area -->
<section class="section blog-single">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 offset-lg-1 col-md-12 col-12">
                <div class="single-inner">
                    <div class="post-thumbnails">
                        <img src="{{ Voyager::image($post->image) }}" style="width: 100%; height: auto;" alt="{{ $post->title }}">
                    </div>
                    <div class="post-details">
                        <div class="detail-inner">
                            <!-- Post meta -->
                            <ul class="custom-flex post-meta">
                                <li>
                                    <a href="javascript:void(0)">
                                        <i class="lni lni-calendar"></i>
                                        {{ $post->created_at->format('dS F Y') }}
                                    </a>
                                </li>
                                <li>
                                    <a href="javascript:void(0)">
                                        <i class="lni lni-comments"></i>
                                        {{ $comments->count() }} Comments
                                    </a>
                                </li>
                                <li>
                                    <a href="javascript:void(0)">
                                        <i class="lni lni-eye"></i>
                                        {{ $post->views }} Views
                                    </a>
                                </li>
                            </ul>
                            <h2 class="post-title">
                                <a href="javascript:void(0)">{{ $post->title }}</a>
                            </h2>
                            <p>{!! $post->body !!}</p>
                            <!-- Post tags and social share -->
                            <div class="post-tags-media">
                                <div class="post-tags popular-tag-widget mb-xl-40">
                                </div>
                                <div class="post-social-media">
                                    <h5 class="share-title">Social Share</h5>
                                    <ul class="custom-flex">
                                        <li>
                                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}" class="facebook" target="_blank">
                                                <i class="lni lni-facebook-original"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="https://twitter.com/intent/tweet?url={{ urlencode(url()->current()) }}" class="twitter" target="_blank">
                                                <i class="lni lni-twitter-original"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="https://plus.google.com/share?url={{ urlencode(url()->current()) }}" class="google" target="_blank">
                                                <i class="lni lni-google"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="https://pinterest.com/pin/create/button/?url={{ urlencode(url()->current()) }}" class="pinterest" target="_blank">
                                                <i class="lni lni-pinterest"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="https://vimeo.com/share?url={{ urlencode(url()->current()) }}" class="vimeo" target="_blank">
                                                <i class="lni lni-vimeo"></i>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <!-- Comments Section -->
                            <div class="post-comments">
                                <h3 class="comment-title">Post comments</h3>
                                <ul class="comments-list">
                                    @foreach($comments as $comment)
                                        <li>
                                            <div class="comment-img">
                                                <i class="lni lni-user avatar" style="font-size: 68px;"></i>
                                            </div>
                                            <div class="comment-desc">
                                                <div class="desc-top">
                                                    <h6>{{ $comment->author_name }}</h6>
                                                    <span class="date">{{ $comment->created_at->format('dS F Y') }}</span>
                                                </div>
                                                <p>{{ $comment->body }}</p>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                            <!-- Comment Form -->
                                <div class="comment-form">
                                    <h3 class="comment-reply-title">Leave a comment</h3>
                                    <form action="{{ route('comments.store') }}" method="POST" enctype="multipart/form-data">
                                        @csrf
                                        <div class="row">
                                            <div class="col-lg-4 col-md-12 col-12">
                                                <div class="form-box form-group">
                                                    <input type="text" name="author_name" class="form-control form-control-custom" placeholder="Your Name" required>
                                                </div>
                                            </div>
                                            <div class="col-lg-4 col-md-6 col-12">
                                                <div class="form-box form-group">
                                                    <input type="email" name="author_email" class="form-control form-control-custom" placeholder="Your Email" required>
                                                </div>
                                            </div>
                                            <div class="col-lg-4 col-md-6 col-12">
                                                <div class="form-box form-group">
                                                    <input type="text" name="subject" class="form-control form-control-custom" placeholder="Your Subject" required>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="form-box form-group">
                                                    <textarea name="body" rows="6" class="form-control form-control-custom" placeholder="Your Comments" required></textarea>
                                                </div>
                                            </div>
                                            {{-- <div class="col-lg-6 col-md-6 col-12">
                                                <div class="form-box form-group">
                                                    <label for="avatar">Upload Your Profile Picture</label>
                                                    <input type="file" name="avatar" id="avatar" class="form-control-file">
                                                </div>
                                            </div> --}}
                                            <div class="col-12">
                                                <input type="hidden" name="post_id" value="{{ $post->id }}">
                                                <div class="button">
                                                    <button type="submit" class="btn mouse-dir white-bg">Post Comment <span class="dir-part"></span></button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Blog Single Area -->
@endsection
