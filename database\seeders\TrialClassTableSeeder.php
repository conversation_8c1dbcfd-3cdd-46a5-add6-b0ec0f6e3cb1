<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use TCG\Voyager\Models\DataType;
use TCG\Voyager\Models\DataRow;
use TCG\Voyager\Models\Permission;

class TrialClassTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create or update DataType for trial classes
        $dataType = DataType::firstOrNew(['name' => 'trial_class']);
        if (!$dataType->exists) {
            $dataType->fill([
                'slug' => 'trial-classes',
                'name' => 'trial_class',
                'display_name_singular' => 'Trial Class',
                'display_name_plural' => 'Trial Classes',
                'icon' => 'voyager-study',
                'model_name' => 'App\\Models\\TrialClass',
                'policy_name' => null,
                'controller' => 'App\\Http\\Controllers\\Admin\\TrialClassController',
                'generate_permissions' => 1,
                'description' => 'Manage trial class registrations with approval workflow',
                'server_side' => 1,
                'details' => json_encode([
                    'order_column' => 'created_at',
                    'order_display_column' => 'first_name',
                    'order_direction' => 'desc',
                    'default_search_key' => 'first_name',
                    'scope' => null
                ])
            ])->save();
        }

        // Create DataRows for trial classes
        $dataRows = [
            [
                'field' => 'id',
                'type' => 'number',
                'display_name' => 'ID',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 1,
            ],
            [
                'field' => 'first_name',
                'type' => 'text',
                'display_name' => 'First Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 2,
            ],
            [
                'field' => 'last_name',
                'type' => 'text',
                'display_name' => 'Last Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 3,
            ],
            [
                'field' => 'email',
                'type' => 'text',
                'display_name' => 'Email',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 4,
            ],
            [
                'field' => 'phone',
                'type' => 'text',
                'display_name' => 'Phone',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 5,
            ],
            [
                'field' => 'country',
                'type' => 'text',
                'display_name' => 'Country',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 6,
            ],
            [
                'field' => 'state',
                'type' => 'text',
                'display_name' => 'State',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 7,
            ],
            [
                'field' => 'choose_course',
                'type' => 'text',
                'display_name' => 'Course',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 8,
            ],
            [
                'field' => 'notes',
                'type' => 'text_area',
                'display_name' => 'Notes',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 9,
            ],
            [
                'field' => 'is_approved',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 10,
                'details' => json_encode([
                    'default' => '0',
                    'options' => [
                        '0' => 'No',
                        '1' => 'Yes'
                    ]
                ])
            ],
            [
                'field' => 'is_read',
                'type' => 'checkbox',
                'display_name' => 'Read',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'order' => 11,
                'details' => json_encode([
                    'on' => 'Read',
                    'off' => 'Unread',
                    'checked' => false
                ])
            ],
            [
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 12,
            ],
            [
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 13,
            ],
        ];

        foreach ($dataRows as $row) {
            $dataRow = DataRow::firstOrNew([
                'data_type_id' => $dataType->id,
                'field' => $row['field']
            ]);

            if (!$dataRow->exists) {
                $dataRow->fill($row)->save();
            }
        }

        // Create permissions
        Permission::generateFor('trial-classes');
    }
}
