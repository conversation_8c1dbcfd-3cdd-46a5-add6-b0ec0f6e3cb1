<?php

namespace App\Models;
use App\Models\Course;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CoursesCategory extends Model
{
    use HasFactory;
    protected $table = 'courses_categories';
    protected $fillable = [
        'title',
        'slug',
    ];

    public function courses()
    {
        return $this->hasMany(Course::class, 'category_id');
    }
}
