/*======================================
    Responsive CSS
========================================*/

/* Tablet Screen */
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .section {
        padding: 60px 0px;
    }

    .section-title {
        margin-bottom: 30px;
        padding: 0px 90px;

        span {
            text-transform: uppercase;
            color: $theme-color;
            display: inline-block;
            margin-bottom: 8px;
            font-size: 13px;
        }

        h2 {
            font-size: 28px;
            margin-top: 5px;
            line-height: 38px;
        }

        p {
            font-size: 13px;
        }
    }

    .section-title.align-left {
        padding: 0;
        padding-right: 200px;
    }

    .breadcrumbs {
        padding-top: 130px;
        padding-bottom: 60px;
    }

    .breadcrumbs .breadcrumbs-content .page-title {
        font-size: 30px;
        margin-bottom: 5px;
        color: #fff;
    }

    .button .btn {
        padding: 12px 25px;
        font-size: 14px;
        font-weight: 500;
    }

    #scrollUp {
        bottom: 55px;
    }

    .navbar-brand img {
        width: 130px;
    }

    .header .search-form {
        display: none !important;
    }

    .hero-area {
        .hero-inner {
            height: auto;
            padding-bottom: 120px;
        }

        .hero-text {
            float: none;
            text-align: center;
            margin-top: 100px !important;

            h5 {
                font-size: 13px;
                padding: 10px 22px;
            }

            h1 {
                font-weight: 700;
                margin-bottom: 20px;
                font-size: 32px !important;
                line-height: 42px !important;
                letter-spacing: 0;
            }
        }
    }

    .hero-area.style2 {
        .hero-text {
            margin-top: 150px !important;
        }

        .hero-inner {
            padding-bottom: 80px;
        }
    }

    .hero-area.style3 .hero-inner {
        height: auto !important;
        padding-bottom: 140px !important;

        .hero-text {
            margin-top: 150px !important;
            text-align: left;

            h5 {
                font-size: 13px;
                padding: 10px 22px;
            }

            h1 {
                font-weight: 700;
                margin-bottom: 20px;
                font-size: 32px !important;
                line-height: 42px !important;
                letter-spacing: 0;
            }
        }
    }

    .header.style3 .header-social {
        display: none;
    }

    .hero-area .hero-image {
        display: none;
    }

    .mission {
        padding: 20px 0;

        .section-title {
            margin: 0;
            padding: 0;

            p {
                font-size: 16px;
            }
        }
    }

    .our-achievement.style3 {
        padding-top: 100px;
        position: relative;
    }

    .features {
        &.style2 {
            .single-feature {
                padding: 30px;

                .serial {
                    font-size: 30px;
                    top: -10px;
                }

                h3 {
                    line-height: 24px;

                    a {
                        font-size: 18px;
                        font-weight: 700;
                    }
                }

                p {
                    margin-top: 15px;
                    font-size: 13px;
                }

                .button {
                    .btn {
                        font-size: 13px;
                    }
                }

            }

        }

        .single-feature {
            padding: 30px;

            .serial {
                font-size: 30px;
                top: -10px;
            }

            h3 {
                line-height: 24px;

                a {
                    font-size: 18px;
                    font-weight: 700;
                }
            }

            p {
                margin-top: 15px;
                font-size: 13px;
            }

            .button {
                .btn {
                    font-size: 13px;
                }
            }

        }
    }

    .about-us {
        .about-left .about-title {
            span {
                font-size: 13px;
            }

            h2 {
                font-size: 28px;
                font-weight: 700;
                padding-bottom: 0;
            }

            p {
                margin: 15px 0;
            }
        }
    }

    .experience .exp-title h2 {
        font-size: 28px;
        line-height: 38px;
    }

    .experience .image {
        position: relative;
        padding: 50px;
        border: 1px solid #eee;
        margin-top: 40px;

        h2 {
            right: 50px;
            bottom: 50px;
        }
    }

    .courses .single-course .bottom-content .review li:last-child {
        display: block;
        margin-top: 4px;
        margin-left: 0;
    }

    .our-achievement {
        .single-achievement {
            padding: 0;

            h3 {
                font-size: 32px;
                margin-bottom: 10px;
            }

            h4 {
                font-size: 15px;
                font-weight: 500;
                margin: 0;
            }
        }
    }

    .courses .single-course .bottom-content {
        text-align: center;
        padding: 20px 30px;

        .review {
            float: left;
            float: none;
            text-align: center;
        }

        .tag {
            float: none;
            text-align: center;
            margin-top: 10px;
        }

    }

    .courses.grid-page {
        padding-top: 30px;
    }

    .event-sidebar {
        margin-top: 40px;
    }

    .course-details .course-overview .overview-course-video iframe {
        height: 350px;
    }

    .events .single-event .bottom-content {
        overflow: hidden;
        border: 1px solid #eee;
        padding: 20px 30px;
        border-top: 30px;
        text-align: center;

        .speaker {
            float: left;
            float: none;
        }

        .time {
            float: right;
            margin-top: 10px;
            float: none;
            text-align: center;
            display: block;
        }
    }

    .events.grid-page {
        padding-top: 30px;
    }

    .course-sidebar {
        margin-top: 40px;
    }

    .teachers .single-team {
        position: relative;

        .image {
            padding-right: 10px;
        }

        &::before {
            display: none;
        }

        &::after {
            position: absolute;
            content: "";
            left: 0;
            bottom: 0;
            height: 5px;
            width: 0%;
            background-color: #0EDC8D;
            -webkit-transition: all 0.4s ease-in-out;
            transition: all 0.4s ease-in-out;
        }

        &:hover::after {
            width: 100% !important;
        }
    }

    .testimonials {
        padding-bottom: 100px;

        .tns-nav {
            bottom: 40px;
        }
    }

    .enroll-section {
        img {
            display: none;
        }

        .enroll {
            margin: 0;
        }
    }

    .work-process .list li .content {
        padding: 25px 20px;
        font-size: 14px;
    }

    .newsletter-area {
        .newsletter-title {
            h2 {
                font-size: 28px;
            }
        }

    }

    .photo-gallery {
        .images {
            display: grid;
            Grid-template-columns: repeat(6, 1fr);
            Grid-gap: 1em 1em;
            Margin-top: 1em;
        }
    }

    .call-action {
        .call-content {
            span {
                color: #fff;
                font-weight: 600;
                display: block;
                margin-bottom: 5px;
            }

            h2 {
                font-size: 28px;
                margin-bottom: 20px;
            }
        }
    }

    .coming-soon .soon-content {
        .text h2 {
            font-size: 28px;
            line-height: 48px;
            margin-bottom: 12px;
        }

        .social-links {
            margin-top: 30px;
            display: block;
        }
    }

    .error-area {
        .error-content {
            h1 {
                font-size: 60px !important;
                margin-bottom: 10px;
                font-weight: 800;
            }

            h2 {
                font-size: 20px !important;
            }
        }
    }

    .mail-success {
        .mail-content {
            h1 {
                font-size: 35px !important;
                margin-bottom: 10px;
                font-weight: 800;
            }

            h2 {
                font-size: 18px !important;
                font-weight: 500 !important;
                margin-top: 10px;
            }

            p {
                margin-top: 15px;
            }
        }
    }

    .latest-news-area {
        .single-head {
            padding: 0px 140px;
        }

        .section-title {
            margin-bottom: 20px;
        }

        .single-news {
            margin-top: 30px;
        }
    }

    .blog-grid-page {
        padding-top: 30px !important;
    }

    .blog-grid-page .single-news {
        margin-bottom: 0 !important;
    }

    .post-details .post-meta li {
        margin-bottom: 8px;
    }

    .latest-news-area.blog-list {
        padding-top: 30px;
    }

    .blog-list .single-news {
        margin-bottom: 0 !important;
    }

    .post-details p {
        margin: 25px 0;
    }

    .pagination {
        margin: 40px 0 0 0;
    }

    .sidebar {
        margin-top: 30px;
    }

    .sidebar .widget {
        padding: 30px;
    }

    .sidebar .widget.search-widget form input {
        padding: 0 80px 0 20px;
    }

    .sidebar .widget.social-widget ul li {
        margin-bottom: 10px;
    }

    .contact-area .contact-address-wrapper {
        padding-right: 0;
    }

    .contact-area .inner-section-title h2 {
        font-weight: 700;
        font-size: 28px;
    }

    .sidebar.service-sidebar .service-category>li>a {
        font-size: 14px;
        font-weight: 500;
        padding: 0;
        padding: 10px 15px;
    }

    .sidebar.service-sidebar .service-category {
        padding: 30px;
    }

    .sidebar.service-sidebar .service-docs {
        padding: 30px;
    }

    .sidebar.service-sidebar .service-quote {
        padding: 30px;
    }

    .sidebar .widget.popular-feeds .single-popular-feed {
        min-height: 102px !important;
    }

    .post-details .post-title {
        line-height: 30px;
    }

    .post-details .post-title a {
        font-size: 24px;
        font-weight: 600;
    }

    .blog-middle-image {
        margin-bottom: 20px;
    }

    .blog-single .sidebar {
        margin-top: 30px;
    }

    .map-section {
        background-color: #fff;
        height: auto;

        iframe {
            height: 300px !important;
        }
    }

    .newsletter-area .mini-call-action {
        margin-top: 30px;
    }

    .contact-us .form-main {
        margin: 0 !important;
        margin-bottom: 35px !important;
    }

    .footer .f-link ul li {
        margin-bottom: 13px;
    }

    .footer .f-about p {
        padding-right: 80px;
    }

    .footer .single-footer {
        padding-top: 35px;
        margin-top: 35px;
    }

    .footer.style2 .f-about {
        padding-right: 0;

        p {
            padding: 0;
        }
    }

    .footer .footer-middle {
        padding-bottom: 70px;
        padding-top: 0;
    }


}

/* Mobile Screen */
@media only screen and (max-width: 767px) {

    .section {
        padding: 50px 0px;
    }

    .section-title {
        margin-bottom: 40px;
        padding: 0px;

        span {
            text-transform: uppercase;
            color: $theme-color;
            display: inline-block;
            margin-bottom: 8px;
            font-size: 12px;
        }

        h2 {
            font-size: 24px;
            margin-top: 3px;
            line-height: 32px;
        }

        p {
            font-size: 13px;
        }
    }

    .section-title.align-left {
        padding: 0;
        padding-right: 0;
    }

    .scroll-top {
        width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: 12px;
    }

    .breadcrumbs {
        padding-top: 130px;
        padding-bottom: 60px;
    }


    .breadcrumbs .breadcrumbs-content {
        margin-bottom: 30px;
        padding: 0;
    }

    .breadcrumbs .breadcrumbs-content p {
        font-size: 13px;
    }

    .breadcrumbs .breadcrumb-nav {
        padding: 10px 20px;
        margin-top: 5px !important;

        li {
            font-size: 13px !important;

            a {
                font-size: 13px !important;
            }
        }
    }

    .breadcrumbs .breadcrumbs-content .page-title {
        font-size: 24px;
        line-height: 35px;
    }

    .button .btn {
        padding: 12px 25px;
        font-size: 14px;
        font-weight: 500;
    }

    #scrollUp {
        bottom: 55px;
    }

    .header .toolbar-area {
        text-align: center;
    }

    .header .toolbar-login {
        float: none;
        margin-top: 15px;
    }

    .header .logo img {
        width: 150px;
    }


    .header .search-form {
        display: none !important;
    }

    .hero-area {
        .hero-inner {
            height: auto;
            padding-bottom: 110px !important;
        }

        .hero-text {
            float: none;
            text-align: center;
            margin-top: 60px !important;

            h5 {
                font-size: 13px;
                padding: 10px 22px;
            }

            h1 {
                font-weight: 700;
                margin-bottom: 20px;
                font-size: 26px !important;
                line-height: 35px !important;
                letter-spacing: 0;
            }
        }

        .tns-nav {
            bottom: 50px;
        }
    }

    .hero-area.style2 .hero-text {
        margin-top: 125px !important;
    }

    .hero-area.style2 .hero-inner {
        padding-bottom: 60px !important;
    }

    .hero-area .hero-image {
        display: none;
    }

    .header.style3 .header-social {
        display: none;
    }

    .hero-area.style3 .hero-inner {
        height: auto !important;
        padding-bottom: 100px !important;

        .hero-text {
            margin-top: 110px !important;
            text-align: left;

            h5 {
                font-size: 13px;
                padding: 10px 22px;
            }

            h1 {
                font-weight: 700;
                margin-bottom: 20px;
                font-size: 26px !important;
                line-height: 35px !important;
                letter-spacing: 0;
            }
        }
    }

    .hero-area .hero-text .video-button {
        position: relative;
        height: 55px;
        width: 55px;
        line-height: 56px;
        text-align: center;
        border-radius: 50%;
        display: inline-block;
        background-color: #fff;
        color: #081828;
        margin-left: 50px;
        margin: 0;
        padding-left: 2px;
    }

    .mission {
        padding: 20px 0;

        .section-title {
            margin: 0;
            padding: 0;

            p {
                font-size: 15px;
                line-height: 26px;
                margin: 10px 0;
            }
        }
    }

    .services .single-service {
        padding: 40px 30px;
    }

    .our-achievement.style3 {
        padding-top: 80px;
        position: relative;
    }

    .features {
        &.style2 {
            .single-feature {
                padding: 30px;
                border: none;
                border-bottom: 1px solid #eee;

                .serial {
                    font-size: 30px;
                    top: -10px;
                }

                h3 {
                    line-height: 24px;

                    a {
                        font-size: 18px;
                        font-weight: 700;
                    }
                }

                p {
                    margin-top: 15px;
                    font-size: 13px;
                }

                .button {
                    .btn {
                        font-size: 13px;
                    }
                }

            }

        }

        .single-feature {
            padding: 30px;
            border: none;
            border-bottom: 1px solid #eee;

            .serial {
                font-size: 30px;
                top: -10px;
            }

            h3 {
                line-height: 24px;

                a {
                    font-size: 18px;
                    font-weight: 700;
                }
            }

            p {
                margin-top: 15px;
                font-size: 13px;
            }

            .button {
                .btn {
                    font-size: 13px;
                }
            }

        }
    }

    .about-us {
        .about-left .about-title {
            span {
                font-size: 13px;
            }

            h2 {
                font-size: 24px;
                font-weight: 700;
                padding-bottom: 0;
                line-height: 32px;
            }

            p {
                margin: 15px 0;
            }
        }


    }

    .experience .left-content {
        padding: 40px 30px;

        .exp-title h2 {
            font-size: 24px;
            margin-bottom: 20px;
            line-height: 28px;
        }
    }


    .experience .image {
        margin-top: 30px;
    }

    .cta-mini {
        text-align: center;
        padding: 0px;
        padding-top: 50px;
    }

    .cta-mini p {
        font-size: 14px;
        line-height: 25px;
    }

    .courses .single-course .bottom-content .review li:last-child {
        display: block;
        margin-top: 4px;
        margin-left: 0;
    }

    .courses.style2 .button {
        margin-top: 50px;
    }

    .our-achievement {
        padding-top: 10px;

        .single-achievement {
            padding: 0;
            margin-top: 40px;

            h3 {
                font-size: 32px;
                margin-bottom: 5px;
            }

            h4 {
                font-size: 15px;
                font-weight: 500;
                margin: 0;
            }
        }
    }

    .courses .single-course .bottom-content {
        text-align: center;
        padding: 20px 30px;

        .review {
            float: left;
            float: none;
            text-align: center;
        }

        .tag {
            float: none;
            text-align: center;
            margin-top: 10px;
        }

    }

    .courses.grid-page {
        padding-top: 20px;
    }

    .events.grid-page {
        padding-top: 20px;
    }

    .event-details .details-content .title {
        font-size: 28px;
    }

    .event-details .details-content .meta-data li {
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .course-sidebar {
        margin-top: 40px;
    }

    .course-details .course-overview .overview-course-video iframe {
        height: 300px;
    }

    .course-details .nav-tabs li {
        display: block;
        width: 100%;
        margin: 0;
        border-bottom: 1px solid #e3e3e3;

        &:last-child {
            border: none;
        }

        button {
            display: block;
            width: 100%;
        }
    }

    .course-details .bottom-content .share {
        float: none;
        margin-top: 15px;
    }

    .course-details .course-curriculum .single-curriculum-section ul.section-content .course-item .section-item-link {
        padding: 0 25px 0 25px;
    }

    .course-details .course-curriculum .single-curriculum-section ul.section-content .course-item .section-item-link {
        padding: 0 25px 0 25px;
        display: block;
        text-align: left;
    }

    .course-details .course-curriculum .single-curriculum-section ul.section-content .course-item .section-item-link .course-item-meta {
        vertical-align: middle;
        text-align: left;
    }

    .course-details .course-curriculum .single-curriculum-section ul.section-content .course-item .section-item-link .course-item-meta .item-meta.duration {
        margin: 0;
    }

    .course-details .course-curriculum .single-curriculum-section ul.section-content .course-item .section-item-link .course-item-meta {
        padding-top: 0;
    }

    .course-details .course-curriculum .single-curriculum-section ul.section-content .course-item .section-item-link .course-item-meta {
        padding-bottom: 20px;
    }

    .course-details .course-curriculum .single-curriculum-section ul.section-content .course-item .section-item-link .item-name {
        padding-top: 15px;
    }

    .course-details .course-curriculum .single-curriculum-section ul.section-content .course-item .section-item-link .course-item-meta .item-meta.count-questions {
        margin: 0;
    }

    .course-details .profile-info {
        margin-top: 30px;
    }

    .comment-form form .form-box .form-control-custom {
        height: 50px;
        margin-bottom: 15px;
    }

    .course-details .course-sidebar .sidebar-widget {
        margin-bottom: 30px;
        padding: 30px;
        border: 1px solid #eee;
    }

    .events .single-event .bottom-content {
        overflow: hidden;
        border: 1px solid #eee;
        padding: 20px 30px;
        border-top: 30px;
        text-align: center;

        .speaker {
            float: left;
            float: none;
        }

        .time {
            float: right;
            margin-top: 10px;
            float: none;
            text-align: center;
            display: block;
        }
    }

    .event-sidebar {
        margin-top: 40px;
    }

    .teachers .single-team {
        position: relative;

        .info-head {
            padding: 22px 40px 35px 20px;
        }

        .image {
            padding-right: 10px;
        }

        &::before {
            display: none;
        }

        &::after {
            position: absolute;
            content: "";
            left: 0;
            bottom: 0;
            height: 5px;
            width: 0%;
            background-color: #0EDC8D;
            -webkit-transition: all 0.4s ease-in-out;
            transition: all 0.4s ease-in-out;
        }

        &:hover::after {
            width: 100% !important;
        }
    }

    .teacher-personal-info .personal-social {
        padding-left: 0;
        margin-top: 25px;
    }

    .testimonials {
        padding-bottom: 100px;

        .tns-nav {
            bottom: 40px;
        }
    }

    .enroll-section {
        img {
            display: none;
        }
    }

    .enroll-section .enroll {
        padding: 40px;
        margin-left: 0;
    }

    .newsletter-area {
        .newsletter-title {
            h2 {
                font-size: 24px;
            }

            p {
                font-size: 13px;
            }
        }

    }

    .newsletter-area.style3 {
        .subscribe-text {
            form {
                padding: 0 !important;

                input {
                    width: 100% !important;
                    height: 55px;
                    padding-right: 165px;
                    padding: 0;
                    padding: 0px 20px;
                }

                .button {
                    position: relative !important;
                    right: 0;
                    top: 0;

                    .btn {
                        width: 100% !important;
                    }
                }
            }
        }
    }

    .call-action {
        .call-content {
            padding: 0;

            span {
                color: #fff;
                font-weight: 600;
                display: block;
                margin-bottom: 5px;
            }

            h2 {
                font-size: 24px;
                margin-bottom: 20px;
            }
        }
    }

    .call-action.style2 .call-content {
        text-align: center;

        .button {
            float: none;
            text-align: center;
            margin-top: 10px;
        }
    }

    .latest-news-area.style2 .single-news.big .title {
        line-height: 28px;

        a {
            font-size: 22px;
        }
    }


    .newsletter-area .section-title {
        padding: 0;
        margin-bottom: 50px;
    }

    .newsletter-area .section-title h2 {
        font-size: 18px;
        line-height: 25px;
    }

    .newsletter-area .subscribe-text input {
        width: 100%;
        display: block;
        margin-bottom: 12px;
    }

    .newsletter-area .subscribe-text {
        padding: 15px;
        border-radius: 5px;
    }

    .newsletter-area .subscribe-text .button {
        display: inline-block;
        width: 100%;

        .btn {
            margin: 0;
            width: 100%;
        }
    }

    .photo-gallery {
        .images {
            display: grid;
            Grid-template-columns: repeat(4, 1fr);
            Grid-gap: 1em 1em;
            Margin-top: 1em;
        }
    }

    .faq .nav-tabs .nav-link {
        display: block;
        margin: 0;
        width: 100%;
        padding: 16px 10px;
        border-bottom: 1px solid #e3e3e3;

        &:last-child {
            border: none;
        }
    }

    .login .form-head {
        padding: 40px;

        form {
            .check-and-pass {
                text-align: center;
            }

            .check-and-pass .lost-pass {
                float: none;
                text-align: center;
                margin-top: 5px;
            }

            .check-and-pass .form-check {
                float: none;
                text-align: center;

                .form-check-input {
                    float: none;
                    display: inline-block;
                    margin-right: 5px;
                }
            }
        }
    }


    .coming-soon .soon-content {
        .box {
            width: 80px;
            height: 80px;
            margin: 0 3px;
            padding-top: 20px;
            margin-bottom: 10px;
        }

        .text h2 {
            font-size: 26px;
            line-height: 48px;
            margin-bottom: 12px;
        }

        .social-links {
            margin-top: 30px;
            display: block;
        }
    }

    .error-area {
        .error-content {
            text-align: center;

            h1 {
                font-size: 50px !important;
                margin-bottom: 10px;
                font-weight: 800;
            }

            h2 {
                font-size: 18px !important;
            }

            .button {
                text-align: center;
                display: inline-block;
                margin-top: 0 !important;

                .btn {
                    width: 180px;
                    display: block;
                    margin-bottom: 10px;

                    &:last-child {
                        margin: 0;
                    }
                }
            }
        }
    }

    .mail-success {
        .mail-content {
            h1 {
                font-size: 30px !important;
                margin-bottom: 10px;
                font-weight: 800;
            }

            h2 {
                font-size: 15px !important;
                font-weight: 500 !important;
                margin-top: 10px;
            }

            p {
                margin-top: 15px;
            }

            .button {
                text-align: center;
                display: inline-block;
                margin-top: 0 !important;

                .btn {
                    width: 180px;
                    display: block;
                    margin-bottom: 10px;

                    &:last-child {
                        margin: 0;
                    }
                }
            }
        }
    }

    .brand-area .section-title {
        padding: 0px;
    }

    .brand-area .clients-logos {
        margin-top: 30px;
    }

    .brand-area .clients-logos img {
        width: 65%;
    }

    .footer .call-action .inner-content .button {
        float: left;
        margin-top: 30px;
    }

    .pricing-table {
        .section-title {
            margin-bottom: 20px;
        }
    }

    .pricing-table .single-table {
        margin-top: 30px;
    }

    .blog-grid-page {
        padding-top: 30px !important;
    }

    .latest-news-area {

        .section-title {
            margin-bottom: 20px;
        }

        .single-news {
            margin-top: 30px;
        }
    }


    .post-details .post-meta li {
        margin-bottom: 8px;
    }

    .blog-grid-page .single-news {
        margin-bottom: 0 !important;
    }

    .post-details p {
        margin: 25px 0;
    }

    .pagination {
        margin: 40px 0 0 0;
    }

    .post-details .post-title {
        line-height: 26px !important;
    }

    .blog-single .post-details blockquote {
        padding: 30px;
    }

    .post-comments {
        margin-top: 40px;
    }

    .comment-form {
        margin-top: 40px;
    }

    .post-details .post-title a {
        font-size: 20px !important;
    }

    .post-details .list {
        padding: 0;
        margin-top: 30px;
    }

    .post-details h3 {
        font-size: 18px !important;
        line-height: 26px !important;
    }

    .post-details .post-tags-media .post-social-media ul li {
        margin: 0;
        margin-left: 7px;
        margin-bottom: 7px;

        &:last-child {
            margin-left: 7px;
            margin-bottom: 7px;
        }
    }

    .post-details .post-tags-media .post-social-media {
        top: -12px;
    }

    .post-comments .comments-list li {
        padding-left: 0;
    }

    .post-comments .comments-list li .comment-img {
        position: relative;
        margin-bottom: 15px;
    }

    .post-comments .comments-list li:not(:first-child) {
        margin: 0;
        margin: 30px 0;
    }

    .sidebar {
        margin-top: 30px;
    }

    .sidebar .widget {
        padding: 30px;
    }

    .sidebar .widget.search-widget form input {
        padding: 0 80px 0 20px;
    }

    .sidebar .widget.social-widget ul li {
        margin-bottom: 10px;
    }

    .sidebar.service-sidebar .service-category>li>a {
        font-size: 14px;
        font-weight: 500;
        padding: 0;
        padding: 10px 15px;
    }

    .sidebar.service-sidebar .service-category {
        padding: 30px;
    }

    .sidebar.service-sidebar .service-docs {
        padding: 30px;
    }

    .sidebar.service-sidebar .service-quote {
        padding: 30px;
    }

    .post-details .post-title {
        line-height: 30px;
    }

    .post-details .post-title a {
        font-size: 24px;
        font-weight: 600;
    }

    .blog-middle-image {
        margin-bottom: 20px;
    }

    .blog-single .sidebar {
        margin-top: 30px;
    }


    .sidebar .widget.popular-feeds .single-popular-feed {
        min-height: 102px !important;
    }

    .map-section {
        background-color: #fff;
        height: auto;

        iframe {
            height: 300px !important;
        }
    }

    .newsletter-area .mini-call-action {
        margin-top: 30px;
    }

    .contact-us .form-main {
        margin: 0 !important;
        margin-bottom: 35px !important;
        padding: 40px !important;
    }

    .footer .logo img {
        width: 150px;
    }

    .footer .call-action .inner-content h2 {
        font-size: 22px;
    }

    .footer .call-action .inner-content {
        padding: 60px 0;

        p {
            margin-top: 16px;
            font-size: 13px;
            font-weight: 500;
            line-height: 23px;

        }

        .button .btn {
            font-size: 13px;
        }
    }


    .footer .f-link ul li {
        margin-bottom: 13px;
    }

    .footer .f-about p {
        padding-right: 0;
    }

    .footer.style2 .f-about {
        padding-right: 0;
    }

    .footer .single-footer {
        padding-top: 20px;
        margin-top: 20px;
    }

    .footer .f-about p {
        padding-right: 0;
        line-height: 24px;
    }

    .footer .footer-middle {
        padding-bottom: 70px;
        padding-top: 0;
    }
}