<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use TCG\Voyager\Models\DataType;
use TCG\Voyager\Models\DataRow;
use TCG\Voyager\Models\Permission;

class TestimonialsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create or update DataType for testimonials
        $dataType = DataType::firstOrNew(['slug' => 'testimonials']);
        if (!$dataType->exists) {
            $dataType->fill([
                'name' => 'testimonials',
                'display_name_singular' => 'Testimonial',
                'display_name_plural' => 'Testimonials',
                'icon' => 'voyager-chat',
                'model_name' => 'App\\Models\\Testimonial',
                'policy_name' => null,
                'controller' => null,
                'generate_permissions' => 1,
                'description' => 'Manage testimonials with approval workflow',
                'server_side' => 1,
                'details' => json_encode([
                    'order_column' => 'created_at',
                    'order_display_column' => 'name',
                    'order_direction' => 'desc',
                    'default_search_key' => 'name',
                    'scope' => null
                ])
            ])->save();
        }

        // Create DataRows for testimonials
        $dataRows = [
            [
                'field' => 'id',
                'type' => 'number',
                'display_name' => 'ID',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 1,
            ],
            [
                'field' => 'name',
                'type' => 'text',
                'display_name' => 'Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 2,
            ],
            [
                'field' => 'country',
                'type' => 'text',
                'display_name' => 'Country',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 3,
            ],
            [
                'field' => 'comment',
                'type' => 'text_area',
                'display_name' => 'Comment',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 4,
            ],
            [
                'field' => 'image',
                'type' => 'image',
                'display_name' => 'Image',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 5,
                'details' => json_encode([
                    'resize' => [
                        'width' => '300',
                        'height' => '300'
                    ],
                    'quality' => '80%',
                    'upsize' => true,
                    'thumbnails' => [
                        [
                            'name' => 'medium',
                            'scale' => '50%'
                        ],
                        [
                            'name' => 'small',
                            'scale' => '25%'
                        ]
                    ]
                ])
            ],
            [
                'field' => 'is_approved',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'order' => 6,
                'details' => json_encode([
                    'default' => '0',
                    'options' => [
                        '0' => 'Pending',
                        '1' => 'Approved'
                    ]
                ])
            ],
            [
                'field' => 'is_read',
                'type' => 'checkbox',
                'display_name' => 'Read',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'order' => 7,
                'details' => json_encode([
                    'on' => 'Read',
                    'off' => 'Unread',
                    'checked' => false
                ])
            ],
            [
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 8,
            ],
            [
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'order' => 9,
            ],
        ];

        foreach ($dataRows as $row) {
            $dataRow = DataRow::firstOrNew([
                'data_type_id' => $dataType->id,
                'field' => $row['field']
            ]);

            if (!$dataRow->exists) {
                $dataRow->fill($row)->save();
            }
        }

        // Create permissions
        Permission::generateFor('testimonials');
    }
}
